const fs = require("fs");
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");

contextBridge.exposeInMainWorld("electronAPI", {
  fileExists: async (filePath) => {
    return await ipcRenderer.invoke("file-exists", filePath);
  },
  updateWindowTitle: (projectName) =>
    ipcRenderer.invoke("update-window-title", projectName),
  getProjectInfo: () => ipcRenderer.invoke("get-project-info"),
  onClearProject: (callback) => {
    return ipcRenderer.on("clear-project", (event, ...args) => {
      callback(event, ...args);
    });
  },
  updateProjectTracking: (projectPath, projectName) =>
    ipcRenderer.invoke("update-project-tracking", projectPath, projectName),
  // File system operations for reading project files
  readFile: (filePath, encoding = "utf8") => {
    try {
      return fs.readFileSync(filePath, encoding);
    } catch (err) {
      console.error("❌ Failed to read file:", err);
      throw err;
    }
  },

  // ASYNC version for enhanced project loading
  readFileAsync: async (filePath, encoding = "utf8") => {
    try {
      return await ipcRenderer.invoke("file:readFile", filePath, encoding);
    } catch (err) {
      console.error("❌ Failed to read file async:", err);
      throw err;
    }
  },

  fileExists: (filePath) => {
    try {
      return fs.existsSync(filePath);
    } catch (err) {
      console.error("❌ Failed to check file existence:", err);
      return false;
    }
  },

  // Add file buffer reading for audio extraction
  readFileBuffer: (filePath) => {
    try {
      // Validate input
      if (!filePath || typeof filePath !== "string" || filePath.trim() === "") {
        throw new Error(`Invalid file path provided: ${filePath}`);
      }

      let cleanPath = filePath.trim();
      // Handle different URL formats
      if (cleanPath.startsWith("file:///")) {
        // Standard file:// URL format
        cleanPath = cleanPath.replace("file:///", "");
      } else if (cleanPath.startsWith("file://")) {
        // Sometimes has only two slashes
        cleanPath = cleanPath.replace("file://", "");
      }
      // On Windows, ensure we have proper drive letter format
      if (process.platform === "win32") {
        // Fix cases where path might be /C:/... instead of C:/...
        if (cleanPath.startsWith("/") && cleanPath.charAt(2) === ":") {
          cleanPath = cleanPath.substring(1);
        }
        // Ensure backslashes are used on Windows
        cleanPath = cleanPath.replace(/\//g, "\\");
      }

      // Check if file exists before trying to read
      if (!fs.existsSync(cleanPath)) {
        throw new Error(`File does not exist: ${cleanPath}`);
      }

      const buffer = fs.readFileSync(cleanPath);
      return buffer;
    } catch (err) {
      console.error("❌ Failed to read file buffer:", err);
      console.error("❌ Original path:", filePath);
      throw err;
    }
  },

  // ASYNC version for enhanced project loading
  readFileBufferAsync: async (filePath) => {
    try {
      return await ipcRenderer.invoke("file:readFileBuffer", filePath);
    } catch (err) {
      console.error("❌ Failed to read file buffer async:", err);
      throw err;
    }
  },

  getFileStat: (filePath) => {
    try {
      return fs.statSync(filePath);
    } catch (err) {
      console.error("Failed to get file stat:", err);
      return null;
    }
  },

  // Enhanced file operations for project loading
  writeFile: async (filePath, data) => {
    try {
      return await ipcRenderer.invoke("file:writeFile", filePath, data);
    } catch (err) {
      console.error("❌ Failed to write file:", err);
      throw err;
    }
  },

  directoryExists: async (dirPath) => {
    try {
      return await ipcRenderer.invoke("file:directoryExists", dirPath);
    } catch (err) {
      console.error("❌ Failed to check directory:", err);
      return false;
    }
  },

  listFiles: async (dirPath) => {
    try {
      return await ipcRenderer.invoke("file:listFiles", dirPath);
    } catch (err) {
      console.error("❌ Failed to list files:", err);
      return [];
    }
  },

  // Save functionality
  saveVideo: (videoPath, buffer, currentHash, aiVideoAnalytics) =>
    ipcRenderer.invoke(
      "save-project",
      videoPath,
      buffer,
      currentHash,
      aiVideoAnalytics
    ),
  enableSaveMenu: (isEnabled) =>
    ipcRenderer.send("enable-save-menu", isEnabled),
  copyVideoFromPath: (filePath, options = {}) =>
    ipcRenderer.invoke("copy-video-file", filePath, options),

  // Project management
  onNewProject: (callback) =>
    ipcRenderer.on("new-project", (event, filePath) => callback(filePath)),

  // Clear Project - IMPORTANT: This was missing
  onClearProject: (callback) => {
    return ipcRenderer.on("clear-project", (event, ...args) => {
      callback(...args);
    });
  },

  // Save functionality with video support
  saveProjectAs: (videoPath, buffer) =>
    ipcRenderer.invoke("save-project-as", videoPath, buffer),

  openProject: () => ipcRenderer.invoke("open-project"),
  getProjectInfo: () => ipcRenderer.invoke("get-project-info"),

  // ENHANCED: Both sync and async project file opening
  openProjectFile: () => ipcRenderer.invoke("open-project-file"),

  // Video file handling for embedded videos
  createVideoFromBuffer: (videoBlob) => {
    const uint8Array = new Uint8Array(videoBlob.buffer);
    const blob = new Blob([uint8Array], { type: videoBlob.mimeType });
    const file = new File([blob], videoBlob.fileName, {
      type: videoBlob.mimeType,
    });
    return {
      file: file,
      url: URL.createObjectURL(blob),
    };
  },

  // File dialog
  openFileDialog: () => ipcRenderer.invoke("dialog:openFile"),
  readFileAsBuffer: async (path) => {
    if (!path || typeof path !== "string" || path.trim() === "") {
      throw new Error(`Invalid path provided to readFileAsBuffer: ${path}`);
    }
    try {
      const result = await ipcRenderer.invoke("file:readAsBuffer", path);
      return result;
    } catch (error) {
      console.error("❌ readFileAsBuffer IPC failed:", error);
      throw error;
    }
  },

  // Menu event listeners
  onMenuSave: (callback) => ipcRenderer.on("save-project", callback),
  removeMenuSave: (callback) =>
    ipcRenderer.removeListener("save-project", callback),

  onMenuSaveAs: (callback) => ipcRenderer.on("save-project-as", callback),
  removeMenuSaveAs: (callback) =>
    ipcRenderer.removeListener("save-project-as", callback),
  // FIXED: Menu open project handler - now supports both callback and property assignment
  onMenuOpenProject: (callback) => {
    if (typeof callback === "function") {
      return ipcRenderer.on("menu-open-project", (event, ...args) => {
        callback(event, ...args);
      });
    }
  },

  // ADDED: Property-style handler storage for React component compatibility
  _menuOpenProjectHandler: null,

  // ADDED: Method to set the handler (for React components)
  setMenuOpenProjectHandler: (handler) => {
    // Remove existing listener
    ipcRenderer.removeAllListeners("menu-open-project");

    // Store the handler
    window.electronAPI._menuOpenProjectHandler = handler;

    // Set up new listener
    if (handler) {
      ipcRenderer.on("menu-open-project", (event, ...args) => {
        console.log("📨 Menu open project event received, calling handler");
        handler(event, ...args);
      });
      console.log("✅ Menu open project handler set successfully");
    }
  },

  // ADDED: Method to remove the handler
  removeMenuOpenProjectHandler: () => {
    ipcRenderer.removeAllListeners("menu-open-project");
    window.electronAPI._menuOpenProjectHandler = null;
    console.log("🧹 Menu open project handler removed");
  },

  onMenuImportMedia: (callback) => ipcRenderer.on("import-media", callback),
  onMenuExport: (callback) => ipcRenderer.on("export-media", callback),
  onMenuExportMarkers: (callback) => ipcRenderer.on("export-markers", callback),

  // Remove listeners
  removeAllListeners: (channel) => {
    return ipcRenderer.removeAllListeners(channel);
  },

  // PYTHON stdin INTEGRATION
  runPythonAnalysis: (payload) =>
    ipcRenderer.invoke("python:run-analysis", payload),
  checkPythonServer: () => ipcRenderer.invoke("python:check-server"),
  startPythonServer: () => ipcRenderer.invoke("python:start-server"),
  stopPythonServer: () => ipcRenderer.invoke("python:stop-server"),
  restartPythonServer: () => ipcRenderer.invoke("python:restart-server"),
  getPythonServerInfo: () => ipcRenderer.invoke("python:server-info"),
  path: {
    join: (...paths) => path.join(...paths),
    basename: (filepath, ext) => path.basename(filepath, ext),
    dirname: (filepath) => path.dirname(filepath),
    extname: (filepath) => path.extname(filepath),
    resolve: (...paths) => path.resolve(...paths),
  },
});

// ADDED: Auto-setup for React component compatibility
window.addEventListener("DOMContentLoaded", () => {
  console.log(
    "📡 Preload script loaded, setting up menu handler compatibility"
  );

  // Create a proxy to make the API work with both callback and property styles
  Object.defineProperty(window.electronAPI, "onMenuOpenProject", {
    get: function () {
      return this._menuOpenProjectHandler;
    },
    set: function (handler) {
      console.log(
        "📝 Setting menu open project handler via property assignment"
      );
      this.setMenuOpenProjectHandler(handler);
    },
    configurable: true,
  });
});
