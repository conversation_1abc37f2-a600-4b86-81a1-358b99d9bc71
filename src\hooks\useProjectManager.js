/* eslint-disable no-unused-vars */
import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON><PERSON> } from "buffer";

const createEmptyProject = () => ({
  metadata: {
    name: "Untitled Project",
    created: new Date().toISOString(),
    modified: new Date().toISOString(),
    duration: 0,
    fps: 30,
    description: "Video editing project",
    tags: ["video", "editing"],
    author: "User",
  },
  video: {
    fileName: null,
    filePath: null,
    fileSize: null,
    duration: 0,
    resolution: { width: 0, height: 0 },
    hasAudio: false,
  },
  timeline: { tracks: [] },
  cutMarkers: [],
  effects: [],
  transitions: [],
  settings: {
    resolution: { width: 1920, height: 1080 },
    frameRate: 30,
    sampleRate: 48000,
  },
});

export const useProjectManager = () => {
  const [dataVideo, setDataVideo] = useState(null);
  const [videoSrc, setVideoSrc] = useState(null);
  const [videoFile, setVideoFile] = useState(null);
  const [projectData, setProjectData] = useState(createEmptyProject());
  const [isModified, setIsModified] = useState(false);
  const [currentFilePath, setCurrentFilePath] = useState(null);
  const [currentFileName, setCurrentFileName] = useState("");
  const [videoSize, setVideoSize] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [modalExportMarkers, setModalExportMarkers] = useState(false);
  const [modalImportMedia, setModalImportMedia] = useState(false);
  const [modalExport, setModalExport] = useState(false);

  const updateProject = useCallback((updates) => {
    setProjectData((prev) => {
      const updated =
        typeof updates === "function" ? updates(prev) : { ...prev, ...updates };
      updated.metadata.modified = new Date().toISOString();
      return updated;
    });
    setIsModified(true);
  }, []);

  const setVideoData = useCallback(
    (file, videoElement) => {
      console.log("🔍 DEBUG: Setting video data", file);

      if (!file) {
        setVideoFile(null);
        if (videoSrc) {
          URL.revokeObjectURL(videoSrc);
          setVideoSrc(null);
        }
        updateProject((prev) => ({
          ...prev,
          video: {
            fileName: null,
            filePath: null,
            fileSize: null,
            duration: 0,
            resolution: { width: 0, height: 0 },
            hasAudio: false,
          },
        }));
        return;
      }

      setVideoFile(file);
      const videoURL = URL.createObjectURL(file);
      setVideoSrc(videoURL);

      const videoInfo = {
        fileName: file.name,
        filePath: file.path || file.webkitRelativePath || file.name,
        fileSize: file.size,
        duration: videoElement?.duration || 0,
        resolution: {
          width: videoElement?.videoWidth || 0,
          height: videoElement?.videoHeight || 0,
        },
        hasAudio: videoElement?.mozHasAudio !== false,
      };

      updateProject((prev) => ({
        ...prev,
        video: videoInfo,
        metadata: {
          ...prev.metadata,
          duration: videoInfo.duration,
        },
      }));

      console.log("✅ Video data set:", videoInfo);
    },
    [videoSrc, updateProject]
  );

  // FIX: Wrap saveProjectAs in useCallback
  const saveProjectAs = useCallback(async () => {
    let buffer;
    try {
      if (videoSrc) {
        if (videoSrc.startsWith("blob:")) {
          const response = await fetch(videoSrc);
          const blob = await response.blob();
          const arrayBuffer = await blob.arrayBuffer();
          buffer = Buffer.from(arrayBuffer);

          const videoPath = videoFile.name;
          const result = await window.electronAPI.saveProjectAs(
            videoPath,
            buffer
          );

          console.log("Check Error (blob):", result.error);
        } else if (videoSrc.startsWith("file://")) {
          const result = await window.electronAPI.copyVideoFromPath(videoSrc);
          console.log("Check Error (file path):", result.error);
        } else {
          alert("Format videoSrc tidak dikenali.");
        }
      }
    } catch (error) {
      alert(`❌ Terjadi kesalahan: ${error.message}`);
      console.error("saveProject error:", error);
    }
  }, [videoSrc, videoFile]); // Add dependencies

  const openProject = useCallback(async () => {
    console.log("🔍 DEBUG: openProject called in React");

    if (!window.electronAPI) {
      console.error("❌ Electron API not available");
      return { success: false, error: "Electron API not available" };
    }

    try {
      const result = await window.electronAPI.openProject();

      if (result.success) {
        console.log("📊 REACT: Project open result:", {
          fileName: result.fileName,
          size: `${(result.fileSize / 1024 / 1024).toFixed(2)} MB`,
          videoMethod: result.videoRestoreMethod,
        });

        setProjectData(result.projectData);
        setCurrentFilePath(result.filePath);
        setCurrentFileName(result.fileName);
        setIsModified(false);

        console.log("📋 REACT: Project data loaded:", {
          name: result.projectData.metadata?.name,
          duration: `${result.projectData.metadata?.duration || 0}s`,
          tracks: result.projectData.timeline?.tracks?.length || 0,
        });

        // Restore video based on method
        if (result.embeddedVideo) {
          console.log("🔄 REACT: Restoring embedded video...");
          try {
            const videoData = window.electronAPI.createVideoFromBuffer(
              result.embeddedVideo
            );
            setVideoFile(videoData.file);
            setVideoSrc(videoData.url);
            console.log("✅ REACT: Embedded video restored to state");
          } catch (videoError) {
            console.error(
              "❌ REACT: Embedded video restore failed:",
              videoError
            );
          }
        } else if (result.externalVideo) {
          console.log("🔄 REACT: Restoring external video...");
          try {
            const videoData = window.electronAPI.createVideoFromBuffer(
              result.externalVideo
            );
            setVideoFile(videoData.file);
            setVideoSrc(videoData.url);
            console.log("✅ REACT: External video restored to state");
          } catch (videoError) {
            console.error(
              "❌ REACT: External video restore failed:",
              videoError
            );
          }
        } else {
          console.log("ℹ️ REACT: No video to restore");
          setVideoFile(null);
          setVideoSrc(null);
        }

        console.log("✅ REACT: Project fully loaded and restored");
      } else {
        console.warn("⚠️ REACT: Project open failed:", result.error);
      }

      return result;
    } catch (error) {
      console.error("❌ REACT: Open project exception:", error);
      return { success: false, error: error.message };
    }
  }, []);

  // FIX: Wrap handleNewProject in useCallback
  const handleNewProject = useCallback((filePath) => {
    if (filePath) {
      console.log("Received filePath:", filePath);

      const original_filename = filePath.split(/[\\/]/).pop();
      const stat = window.electronAPI.getFileStat(filePath);
      const video_size_mb = stat ? (stat.size / 1024 / 1024).toFixed(2) : "N/A";

      setVideoSrc(filePath);
      setCurrentFilePath(filePath);
      setVideoFile(true);
      setVideoSize(video_size_mb);
      setCurrentFileName(original_filename);

      const video = document.createElement("video");
      video.preload = "metadata";
      video.src = `file://${filePath}`;

      window.electronAPI.enableSaveMenu(true);
    } else {
      console.log("No filePath received");
    }
  }, []);

  // FIX: Wrap menu handlers in useCallback
  const handleMenuImportMedia = useCallback(() => {
    setModalImportMedia(true);
  }, []);

  const handleMenuExport = useCallback(() => {
    setModalExport(true);
  }, []);

  const handleExportMarkers = useCallback(() => {
    setModalExportMarkers(true);
  }, []);

  const handleMenuSaveAs = useCallback(() => {
    console.log("🔍 DEBUG: Menu Save As triggered");
    saveProjectAs();
  }, [saveProjectAs]);

  const handleClearProject = useCallback(() => {
    setVideoSrc(null);
    setDataVideo(null);
    setCurrentFilePath(null);
    setVideoFile(null);
    setVideoSize(null);
    setCurrentFileName(null);
    window.electronAPI.enableSaveMenu(false);
  }, []);

  useEffect(() => {
    if (!window.electronAPI) {
      return;
    }

    window.electronAPI.onNewProject(handleNewProject);
    window.electronAPI.onMenuSaveAs(handleMenuSaveAs);
    window.electronAPI.onMenuImportMedia(handleMenuImportMedia);
    window.electronAPI.onMenuExport(handleMenuExport);
    window.electronAPI.onMenuExportMarkers(handleExportMarkers);
    window.electronAPI.onClearProject(handleClearProject);

    return () => {
      window.electronAPI.removeAllListeners("save-project-as");
    };
  }, [
    handleNewProject,
    handleMenuSaveAs,
    handleMenuImportMedia,
    handleMenuExport,
    handleExportMarkers,
    handleClearProject,
  ]);

  return {
    projectData,
    isModified,
    currentFilePath,
    currentFileName,
    isSaving,
    videoSrc,
    videoFile,
    modalImportMedia,
    setVideoSrc,
    setModalImportMedia,
    updateProject,
    modalExport,
    modalExportMarkers,
    dataVideo,
    setDataVideo,
    setModalExportMarkers,
    setModalExport,
    saveProjectAs,
    openProject,
    setVideoData,
    handleNewProject,
    setVideoFile,
  };
};
