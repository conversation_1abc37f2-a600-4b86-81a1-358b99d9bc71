/* eslint-disable no-undef */
import { execSync } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import path from 'path';

const WEIGHTS_DIR = 'electron/python-scripts/weights';
const EXT_WEIGHTS_DIR = 'electron/python-scripts/ext_weights';

// Create weights directories if they don't exist
if (!existsSync(WEIGHTS_DIR)) {
  mkdirSync(WEIGHTS_DIR, { recursive: true });
}

if (!existsSync(EXT_WEIGHTS_DIR)) {
  mkdirSync(EXT_WEIGHTS_DIR, { recursive: true });
}

// Main MMAudio model weights
const MMAUDIO_WEIGHTS = [
  {
    name: 'mmaudio_small_44k.pth', 
    url: 'https://huggingface.co/hkchengrex/MMAudio/resolve/main/weights/mmaudio_small_44k.pth',
    dir: WEIGHTS_DIR,
    size: '629MB'
  },
  {
    name: 'mmaudio_small_16k.pth', 
    url: 'https://huggingface.co/hkchengrex/MMAudio/resolve/main/weights/mmaudio_small_16k.pth',
    dir: WEIGHTS_DIR,
    size: '629MB'
  }
];

// External weights (VAE, vocoder, visual features)
const EXT_WEIGHTS = [
  {
    name: 'best_netG.pt',
    url: 'https://huggingface.co/hkchengrex/MMAudio/resolve/main/ext_weights/best_netG.pt',
    dir: EXT_WEIGHTS_DIR,
    size: '449MB'
  },
  {
    name: 'synchformer_state_dict.pth',
    url: 'https://huggingface.co/hkchengrex/MMAudio/resolve/main/ext_weights/synchformer_state_dict.pth',
    dir: EXT_WEIGHTS_DIR,
    size: '950MB'
  },
  {
    name: 'v1-44.pth',
    url: 'https://huggingface.co/hkchengrex/MMAudio/resolve/main/ext_weights/v1-44.pth',
    dir: EXT_WEIGHTS_DIR,
    size: '1.22GB'
  },
  {
    name: 'v1-16.pth',
    url: 'https://huggingface.co/hkchengrex/MMAudio/resolve/main/ext_weights/v1-16.pth',
    dir: EXT_WEIGHTS_DIR,
    size: '687MB'
  }
];

// Combine all weights
const ALL_WEIGHTS = [...MMAUDIO_WEIGHTS, ...EXT_WEIGHTS];

console.log('🎵 Downloading MMAudio model weights and external dependencies...');
console.log(`📦 Total files to download: ${ALL_WEIGHTS.length}`);

for (const weight of ALL_WEIGHTS) {
  const filepath = path.join(weight.dir, weight.name);
  
  if (existsSync(filepath)) {
    console.log(`✅ ${weight.name} already exists, skipping...`);
    continue;
  }
  
  console.log(`📥 Downloading ${weight.name} (${weight.size}) to ${weight.dir}...`);
  
  try {
    let downloadCmd;
    
    if (process.platform === 'win32') {
      // Windows: Use curl (built-in since Windows 10)
      downloadCmd = `curl -L --fail --progress-bar -o "${filepath}" "${weight.url}"`;
    } else {
      // macOS/Linux: Try wget first, fallback to curl
      try {
        execSync('which wget', { stdio: 'ignore' });
        downloadCmd = `wget --progress=bar --show-progress -O "${filepath}" "${weight.url}"`;
      } catch {
        downloadCmd = `curl -L --fail --progress-bar -o "${filepath}" "${weight.url}"`;
      }
    }
    
    execSync(downloadCmd, { stdio: 'inherit' });
    console.log(`✅ Downloaded ${weight.name}`);
  } catch (error) {
    console.error(`❌ Failed to download ${weight.name}:`, error.message);
    console.log(`💡 Try downloading manually from: ${weight.url}`);
    process.exit(1);
  }
}

console.log('🎉 MMAudio weights download completed!');
console.log(`📁 Main weights saved to: ${WEIGHTS_DIR}`);
console.log(`📁 External weights saved to: ${EXT_WEIGHTS_DIR}`);