import { useEffect, useRef, useCallback } from "react";
import { useProjectManager } from "../../hooks/useProjectManager";
import { useSaveProject } from "../../hooks/useSaveProject.js";

const VideoPreview = ({
  videoRef,
  videoFile,
  videoSrc,
  isReplaying,
  onTimeUpdate,
  onVideoLoaded,
  segments = [],
  isPlaying = false,
  currentTime = 0,
}) => {
  const { setDataVideo, setVideoSrc, setVideoFile } = useProjectManager();
  const activeReplacementAudioRef = useRef(null);
  const lastActiveSegmentRef = useRef(null);
  const audioSyncTimeRef = useRef(0);
  const audioInitializedRef = useRef(new Set()); // Track which audio files are ready

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;
    if (!window.electronAPI) {
      return;
    }
    setDataVideo(videoFile);
    setVideoSrc(videoSrc);
    setVideoFile(videoFile);
    window.electronAPI.enableSaveMenu(true);
  }, [videoSrc, videoRef, videoFile]);

  // Memoized function to find active segment
  const findActiveSegment = useCallback((time, segmentList) => {
    return segmentList.find((segment) => {
      return (
        segment.hasReplacedAudio &&
        segment.shouldMuteOriginal &&
        time >= segment.start &&
        time <= segment.end
      );
    });
  }, []);

  // OPTIMIZED AUDIO OVERRIDE LOGIC - Better first-play experience
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement || !segments.length) {
      return;
    }

    const activeSegment = findActiveSegment(currentTime, segments);
    const lastActiveSegment = lastActiveSegmentRef.current;

    // More relaxed sync timing after initial load
    const isFirstPlay = !audioInitializedRef.current.has(activeSegment?.id);
    const syncInterval = isFirstPlay ? 0.3 : 1.0; // Sync every 300ms first time, then every 1s
    const shouldSync =
      Math.abs(currentTime - audioSyncTimeRef.current) > syncInterval;
    const segmentChanged = activeSegment?.id !== lastActiveSegment?.id;

    if (!segmentChanged && !shouldSync) {
      return; // Skip this update to reduce audio interruptions
    }

    audioSyncTimeRef.current = currentTime;

    if (activeSegment) {
      // Mute original video audio
      if (!videoElement.muted) {
        videoElement.muted = true;
        console.log("🔇 MUTED original video for segment:", activeSegment.name);
      }

      // Handle replacement audio
      const replacementAudio = activeSegment.audioElement;
      if (replacementAudio) {
        // Wait for audio to be ready before aggressive syncing
        const isAudioReady = replacementAudio.readyState >= 2; // HAVE_CURRENT_DATA

        if (!isAudioReady && isFirstPlay) {
          // For first play, wait for audio to be ready
          replacementAudio.addEventListener(
            "canplay",
            () => {
              audioInitializedRef.current.add(activeSegment.id);
              console.log("🎵 Audio ready for segment:", activeSegment.name);
            },
            { once: true }
          );
        }

        // Calculate proper looped audio time
        const segmentRelativeTime = currentTime - activeSegment.start;
        const audioDuration =
          replacementAudio.duration || activeSegment.audioDuration || 10;

        // Use modulo for seamless looping
        const loopedAudioTime = segmentRelativeTime % audioDuration;
        const currentAudioTime = replacementAudio.currentTime;
        const timeDiff = Math.abs(loopedAudioTime - currentAudioTime);

        // More lenient sync threshold for smoother playback
        const syncThreshold = isFirstPlay ? 0.5 : 0.3; // 500ms first time, then 300ms

        if (timeDiff > syncThreshold) {
          replacementAudio.currentTime = loopedAudioTime;
          if (isFirstPlay) {
            console.log(
              `🔄 Initial audio sync: ${currentAudioTime.toFixed(
                2
              )}s → ${loopedAudioTime.toFixed(2)}s`
            );
            audioInitializedRef.current.add(activeSegment.id);
          }
        }

        // Handle play/pause state
        if (isPlaying && replacementAudio.paused) {
          // Add small delay for first play to ensure audio is ready
          if (isFirstPlay && !isAudioReady) {
            setTimeout(() => {
              if (!replacementAudio.paused) return; // Already playing
              replacementAudio.play().catch((err) => {
                if (err.name !== "AbortError") {
                  console.warn("Audio play failed:", err.name);
                }
              });
            }, 100);
          } else {
            replacementAudio.play().catch((err) => {
              if (err.name !== "AbortError") {
                console.warn("Audio play failed:", err.name);
              }
            });
          }
        } else if (!isPlaying && !replacementAudio.paused) {
          replacementAudio.pause();
        }

        activeReplacementAudioRef.current = replacementAudio;
      }
    } else {
      // Restore original video audio
      if (videoElement.muted) {
        videoElement.muted = false;
        console.log("🔊 RESTORED original video audio");
      }

      // Pause replacement audio only if we had an active segment before
      if (lastActiveSegment) {
        segments.forEach((segment) => {
          if (segment.audioElement && !segment.audioElement.paused) {
            segment.audioElement.pause();
          }
        });
      }

      activeReplacementAudioRef.current = null;
    }

    lastActiveSegmentRef.current = activeSegment;
  }, [currentTime, segments, isPlaying, videoRef, findActiveSegment]);

  // Separate effect for handling video events (play/pause/seek)
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleSeeking = () => {
      console.log("🔍 Video seeking - pausing all replacement audio");
      segments.forEach((segment) => {
        if (segment.audioElement && !segment.audioElement.paused) {
          segment.audioElement.pause();
        }
      });
      // Reset sync timer to force immediate resync after seek
      audioSyncTimeRef.current = 0;
    };

    const handleSeeked = () => {
      console.log("✅ Video seek completed");
      // Force immediate audio sync after seek
      audioSyncTimeRef.current = 0;
    };

    const handlePlay = () => {
      console.log("▶️ Video play event");
    };

    const handlePause = () => {
      console.log("⏸️ Video pause event");
      // Pause all replacement audio immediately
      segments.forEach((segment) => {
        if (segment.audioElement && !segment.audioElement.paused) {
          segment.audioElement.pause();
        }
      });
    };

    videoElement.addEventListener("seeking", handleSeeking);
    videoElement.addEventListener("seeked", handleSeeked);
    videoElement.addEventListener("play", handlePlay);
    videoElement.addEventListener("pause", handlePause);

    return () => {
      videoElement.removeEventListener("seeking", handleSeeking);
      videoElement.removeEventListener("seeked", handleSeeked);
      videoElement.removeEventListener("play", handlePlay);
      videoElement.removeEventListener("pause", handlePause);
    };
  }, [segments, videoRef]);

  // CLEANUP ON UNMOUNT
  useEffect(() => {
    return () => {
      segments.forEach((segment) => {
        if (segment.audioElement && !segment.audioElement.paused) {
          segment.audioElement.pause();
        }
      });
    };
  }, []);

  useSaveProject(videoSrc, videoFile);

  if (!videoSrc) return null;

  return (
    <div className={`video-preview-wrapper ${isReplaying ? "replacing" : ""}`}>
      <video
        ref={videoRef}
        src={videoSrc}
        className="h-full"
        controls
        muted={false} // This will be controlled by the audio override logic
        onTimeUpdate={onTimeUpdate}
        onLoadedMetadata={onVideoLoaded}
        playsInline
      />
    </div>
  );
};

export default VideoPreview;
