/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
import { app, BrowserWindow, Menu, shell, ipcMain, dialog } from "electron";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { spawn, exec } from "child_process";
import remoteMain from "@electron/remote/main/index.js";
import http from "http"; // CRITICAL: Add this import

// Ensure we have access to fs.promises
import { promises as fsPromises } from "fs";

remoteMain.initialize();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Splash Screen
let splashWindow;

let pythonServerProcess = null;
let serverStartupPromise = null;

// PYTHON SERVER MANAGEMENT
const PYTHON_SERVER_PORT = 8000;
const PYTHON_SERVER_URL = `http://127.0.0.1:${PYTHON_SERVER_PORT}`;

// CRITICAL FIX: Replace fetch with Node.js http module
function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      const urlObj = new URL(url);
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || 80,
        path: urlObj.pathname + urlObj.search,
        method: options.method || "GET",
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
        timeout: 0, // 0 means no timeout
      };

      const req = http.request(requestOptions, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          try {
            resolve({
              ok: res.statusCode >= 200 && res.statusCode < 300,
              status: res.statusCode,
              json: () => Promise.resolve(JSON.parse(data)),
              text: () => Promise.resolve(data),
            });
          } catch (parseError) {
            resolve({
              ok: res.statusCode >= 200 && res.statusCode < 300,
              status: res.statusCode,
              json: () => Promise.reject(parseError),
              text: () => Promise.resolve(data),
            });
          }
        });
      });

      req.on("error", (error) => {
        console.error("HTTP request error:", error.message);
        reject(error);
      });

      req.on("timeout", () => {
        req.destroy();
        reject(new Error("Request timeout"));
      });

      // Write request body if provided
      if (options.body) {
        req.write(options.body);
      }

      req.end();
    } catch (error) {
      reject(error);
    }
  });
}

import os from "os";

// Add this function
function logToFile(message) {
  const logPath = path.join(__dirname, "plank-debug.log");
  const timestamp = new Date().toISOString();
  fs.appendFileSync(logPath, `[${timestamp}] ${message}\n`);
}

// ADD THESE HELPER FUNCTIONS HERE
const isDev = !app.isPackaged; // Change back to this

function getPythonScriptPath() {
  if (isDev) {
    // Dev: use the electron/python-scripts folder
    return path.join(__dirname, "python-scripts", "sound_post_server.py");
  } else {
    // Production: look in extraResources
    return path.join(
      process.resourcesPath,
      "python-scripts",
      "sound_post_server.py"
    );
  }
}

function getPythonEnvironmentPath() {
  if (isDev) {
    // Dev: use .venv in project root
    return path.join(__dirname, "python-scripts", ".venv");
  } else {
    // Production: create .venv next to the Python script
    console.log(
      `getPythonEnvironmentPath: process.resourcesPath = ${process.resourcesPath}`
    );
    logToFile(
      `getPythonEnvironmentPath: process.resourcesPath = ${process.resourcesPath}`
    );
    return path.join(process.resourcesPath, "python-scripts", ".venv");
  }
}

function getRequirementsPath() {
  if (isDev) {
    // Dev: look in python-scripts folder
    return path.join(__dirname, "python-scripts", "requirements.txt");
  } else {
    // Production: look in extraResources
    return path.join(
      process.resourcesPath,
      "python-scripts",
      "requirements.txt"
    );
  }
}

async function checkPythonInstallation() {
  return new Promise((resolve) => {
    exec("python --version", (error, stdout, stderr) => {
      if (error) {
        // Try python3
        exec("python3 --version", (error3, stdout3, stderr3) => {
          if (error3) {
            resolve({ installed: false, command: null });
          } else {
            resolve({
              installed: true,
              command: "python3",
              version: stdout3.trim(),
            });
          }
        });
      } else {
        resolve({ installed: true, command: "python", version: stdout.trim() });
      }
    });
  });
}

async function installPythonDependencies() {
  console.log("🐍 === INSTALLING PYTHON DEPENDENCIES ===");
  logToFile("🐍 === INSTALLING PYTHON DEPENDENCIES ===");

  const VENV_DIR = getPythonEnvironmentPath();
  const requirementsPath = getRequirementsPath();
  const isWindows = process.platform === "win32";

  try {
    // STEP 1: Check Python installation
    console.log("📋 Step 1: Checking Python installation...");
    const pythonCheck = await checkPythonInstallation();

    if (!pythonCheck.installed) {
      throw new Error("Python is not installed or not in PATH");
    }
    console.log(
      `✅ Found Python: ${pythonCheck.command} (${pythonCheck.version})`
    );
    logToFile(`Found Python: ${pythonCheck.command} (${pythonCheck.version})`);

    // STEP 2: Create .venv if not exists
    console.log("📋 Step 2: Creating virtual environment...");
    if (!fs.existsSync(VENV_DIR)) {
      // Make sure parent directory exists
      fs.mkdirSync(path.dirname(VENV_DIR), { recursive: true });

      await new Promise((resolve, reject) => {
        console.log("🔨 Creating .venv...");
        console.log(
          `  Executing: ${pythonCheck.command} -m venv "${VENV_DIR}"`
        );
        exec(
          `${pythonCheck.command} -m venv "${VENV_DIR}"`,
          {
            timeout: 60000,
            cwd: path.dirname(VENV_DIR),
          },
          (error, stdout, stderr) => {
            if (error) {
              reject(new Error(`Failed to create .venv: ${error.message}`));
            } else {
              console.log("✅ .venv created successfully");
              logToFile("✅ .venv created successfully");
              resolve();
            }
          }
        );
      });
    } else {
      console.log("✅ .venv already exists");
      logToFile("✅ .venv already exists");
    }

    // STEP 3: Install dependencies
    console.log("📋 Step 3: Installing dependencies...");
    const venvPipPath = isWindows
      ? path.join(VENV_DIR, "Scripts", "pip.exe")
      : path.join(VENV_DIR, "bin", "pip");

    if (!fs.existsSync(venvPipPath)) {
      throw new Error(`Virtual environment pip not found at: ${venvPipPath}`);
    }

    // Check if requirements.txt exists
    if (!fs.existsSync(requirementsPath)) {
      console.log(`⚠️ Requirements.txt not found at: ${requirementsPath}`);
      console.log("📦 Installing basic packages only...");

      await new Promise((resolve, reject) => {
        const installCommand = `"${venvPipPath}" install fastapi uvicorn[standard] pydantic`;
        exec(installCommand, { timeout: 300000 }, (error, stdout, stderr) => {
          if (error) {
            reject(
              new Error(`Failed to install basic packages: ${error.message}`)
            );
          } else {
            console.log("✅ Basic packages installed successfully");
            resolve();
          }
        });
      });
    } else {
      console.log(`✅ Found requirements.txt: ${requirementsPath}`);

      await new Promise((resolve, reject) => {
        const installCommand = `"${venvPipPath}" install -r "${requirementsPath}"`;
        console.log(`📦 Installing from requirements: ${installCommand}`);

        const childProcess = exec(
          installCommand,
          { timeout: 300000 },
          (error, stdout, stderr) => {
            if (error) {
              reject(
                new Error(
                  `Failed to install from requirements: ${error.message}`
                )
              );
            } else {
              console.log(
                "✅ Dependencies from requirements.txt installed successfully"
              );
              resolve();
            }
          }
        );

        // Print output in real-time
        childProcess.stdout.on("data", (data) => {
          process.stdout.write(data);
        });

        childProcess.stderr.on("data", (data) => {
          process.stderr.write(data);
        });
      });
    }

    console.log("🎉 === PYTHON DEPENDENCIES INSTALLATION COMPLETE ===");
    logToFile("=== PYTHON DEPENDENCIES INSTALLATION COMPLETE ===");

    return {
      success: true,
      venvDir: VENV_DIR,
      pythonCommand: pythonCheck.command,
      pythonVersion: pythonCheck.version,
      requirementsFound: fs.existsSync(requirementsPath),
    };
  } catch (error) {
    console.error("❌ === PYTHON DEPENDENCIES INSTALLATION FAILED ===");
    console.error("❌ Error:", error.message);
    logToFile(
      `=== PYTHON DEPENDENCIES INSTALLATION FAILED ===\nError: ${error.message}`
    );

    return {
      success: false,
      error: error.message,
      suggestion: "Check Python installation and permissions",
    };
  }
}

async function startPythonServer() {
  if (serverStartupPromise) {
    return serverStartupPromise;
  }

  serverStartupPromise = new Promise(async (resolve) => {
    try {
      console.log("🔍 Checking if Python server is already running...");

      // Check if server is already running
      try {
        const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/health`);
        if (response.ok) {
          console.log("✅ Python server already running");
          resolve({ success: true, alreadyRunning: true });
          return;
        }
      } catch (e) {
        console.log("🔍 Server not running, starting new instance...");
      }

      // Check Python installation
      const pythonCheck = await checkPythonInstallation();
      if (!pythonCheck.installed) {
        resolve({
          success: false,
          error: "Python is not installed or not in PATH",
        });
        return;
      }

      // Install dependencies
      const depInstall = await installPythonDependencies();
      if (!depInstall.success) {
        resolve({
          success: false,
          error: "Failed to install Python dependencies",
          details: depInstall.error,
        });
        return;
      }

      // Find the Python script
      const serverPath = getPythonScriptPath();
      if (!fs.existsSync(serverPath)) {
        resolve({
          success: false,
          error: `Python script not found at: ${serverPath}`,
        });
        return;
      }

      console.log(`🚀 Starting Python server from: ${serverPath}`);

      // Use venv python if it exists, otherwise use system python
      const venvDir = getPythonEnvironmentPath();
      const isWindows = process.platform === "win32";
      const venvPythonPath = isWindows
        ? path.join(venvDir, "Scripts", "python.exe")
        : path.join(venvDir, "bin", "python");

      const pythonCommand = fs.existsSync(venvPythonPath)
        ? venvPythonPath
        : pythonCheck.command;

      console.log(`🐍 Using Python: ${pythonCommand}`);

      // Start Python process
      pythonServerProcess = spawn(pythonCommand, [serverPath], {
        stdio: ["pipe", "pipe", "pipe"],
        cwd: path.dirname(serverPath),
        env: { ...process.env, PYTHONUNBUFFERED: "1" },
      });

      let serverOutput = "";
      let serverError = "";

      pythonServerProcess.stdout.on("data", (data) => {
        const output = data.toString();
        serverOutput += output;
        console.log(`🐍 Server stdout: ${output.trim()}`);
      });

      pythonServerProcess.stderr.on("data", (data) => {
        const error = data.toString();
        serverError += error;
        console.error(`🐍 Server stderr: ${error.trim()}`);
      });

      pythonServerProcess.on("close", (code) => {
        console.log(`🐍 Python server process exited with code ${code}`);
        pythonServerProcess = null;
        serverStartupPromise = null;
      });

      pythonServerProcess.on("error", (error) => {
        console.error("🐍 Failed to start Python server:", error);
        resolve({
          success: false,
          error: `Failed to start Python server: ${error.message}`,
        });
      });

      // Wait for server to be ready
      let attempts = 0;
      const maxAttempts = 30;

      const checkServer = async () => {
        attempts++;
        console.log(`🔍 Health check attempt ${attempts}/${maxAttempts}`);

        try {
          const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/health`);
          if (response.ok) {
            console.log("✅ Python AI server is ready!");
            resolve({
              success: true,
              port: PYTHON_SERVER_PORT,
              url: PYTHON_SERVER_URL,
            });
          } else {
            throw new Error(`Server responded with status ${response.status}`);
          }
        } catch (error) {
          if (attempts >= maxAttempts) {
            resolve({
              success: false,
              error: "Python server failed to start within 30 seconds",
              serverOutput,
              serverError,
            });
          } else {
            setTimeout(checkServer, 1000);
          }
        }
      };

      setTimeout(checkServer, 2000);
    } catch (error) {
      console.error("❌ Error in startPythonServer:", error);
      resolve({
        success: false,
        error: error.message,
      });
    }
  });

  return serverStartupPromise;
}

function stopPythonServer() {
  if (pythonServerProcess) {
    console.log("🛑 Stopping Python server...");
    logToFile("Stopping Python server...");
    pythonServerProcess.kill("SIGTERM");
    pythonServerProcess = null;
    serverStartupPromise = null;
  }
}

// PYTHON SERVER INTEGRATION - HTTP API calls with proper error handling
ipcMain.handle("python:run-analysis", async (event, payload) => {
  try {
    console.log("🔄 Analysis request received");

    // Make sure server is running
    if (!pythonServerProcess) {
      console.log("🚀 Starting Python server for analysis...");
      const serverResult = await startPythonServer();
      if (!serverResult.success) {
        return {
          success: false,
          error: "Python AI server failed to start",
          details: serverResult.error,
        };
      }
    }

    console.log("🔄 Sending analysis request to Python server...");

    const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/analyze`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      let errorMessage = `Server responded with status ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorData.error || errorMessage;
      } catch (e) {
        // Response might not be JSON
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();

    console.log("✅ Analysis completed successfully");

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ Python analysis failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

// Check if Python server is running
ipcMain.handle("python:check-server", async () => {
  try {
    const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/health`);
    if (response.ok) {
      try {
        const data = await response.json();
        return { running: true, status: data };
      } catch (e) {
        return { running: true, status: { message: "Server responding" } };
      }
    } else {
      return {
        running: false,
        error: `Server returned status ${response.status}`,
      };
    }
  } catch (error) {
    return { running: false, error: error.message };
  }
});

// Manual server start
ipcMain.handle("python:start-server", async () => {
  return await startPythonServer();
});

// Stop server
ipcMain.handle("python:stop-server", async () => {
  stopPythonServer();
  return { success: true };
});

// Get server info
ipcMain.handle("python:server-info", async () => {
  return {
    url: PYTHON_SERVER_URL,
    port: PYTHON_SERVER_PORT,
    running: !!pythonServerProcess,
  };
});

// Debug function
async function debugPythonSetup() {
  console.log("🔍 === PYTHON DEBUG INFORMATION ===");

  // Check Python installation
  const pythonCheck = await checkPythonInstallation();
  console.log("🐍 Python installation:", pythonCheck);

  // Check for Python scripts
  const possibleScriptPaths = [
    path.join(__dirname, "python-scripts", "sound_post_server.py"),
  ];

  console.log("📄 Python script locations:");
  possibleScriptPaths.forEach((scriptPath) => {
    const exists = fs.existsSync(scriptPath);
    console.log(`  ${scriptPath}: ${exists ? "✅ Found" : "❌ Not found"}`);
  });

  // Check for requirements.txt
  const possibleReqPaths = [
    path.join(__dirname, "python-scripts", "requirements.txt"),
  ];

  console.log("📋 Requirements.txt locations:");
  possibleReqPaths.forEach((reqPath) => {
    const exists = fs.existsSync(reqPath);
    console.log(`  ${reqPath}: ${exists ? "✅ Found" : "❌ Not found"}`);
  });

  // Test HTTP connection
  try {
    const response = await makeHttpRequest(`${PYTHON_SERVER_URL}/health`);
    console.log(
      `🌐 Server health: ${
        response.ok ? "✅ Responding" : "❌ Not responding"
      } (status: ${response.status})`
    );
  } catch (error) {
    console.log(`🌐 Server health: ❌ Connection failed - ${error.message}`);
  }

  console.log("🔍 === END DEBUG INFORMATION ===");
}

ipcMain.handle("python:debug", async () => {
  await debugPythonSetup();
  return { debug: "complete" };
});

function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 1024,
    height: 768,
    frame: false,
    alwaysOnTop: true,
    transparent: false,
    show: false,
    center: true,
    resizable: false,
    backgroundColor: "#000",
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // Try the correct path
  const splashPath = path.join(__dirname, "splash.html");

  if (fs.existsSync(splashPath)) {
    splashWindow.loadFile(splashPath);
  }

  // Show splash window after it's loaded to prevent white flash
  splashWindow.webContents.once("did-finish-load", () => {
    splashWindow.show();
  });

  // Handle loading errors
  splashWindow.webContents.on(
    "did-fail-load",
    (event, errorCode, errorDescription) => {
      console.error("❌ Splash window failed to load:", errorDescription);
    }
  );

  // Clean up splash window reference when closed
  splashWindow.on("closed", () => {
    // run python
    splashWindow = null;
    console.log("CHECK SPLASH CLOSED");
  });
}

// Store current project path
let currentProjectPath = null;
let menuItemSave;
let menuItemSaveAs;
let mainWindow = null;
let currentProjectName = "Untitled";
function createWindow() {
  const win = new BrowserWindow({
    show: false,
    // Remove this line: title: `${currentProjectName} - MCL Soundpost One`,
    webPreferences: {
      preload: path.join(__dirname, "preload.cjs"),
      contextIsolation: true,
      nodeIntegration: false,
      sandbox: false,
    },
  });
  mainWindow = win;
  remoteMain.enable(win.webContents);

  // Set initial title when window is created
  updateWindowTitle(null);
  if (isDev) {
    win.loadURL("http://localhost:5173");
    win.webContents.openDevTools();
  } else {
    const indexHtml = path.join(__dirname, "../dist/index.html");
    win.loadFile(indexHtml);
  }

  win.webContents.on(
    "did-fail-load",
    (event, errorCode, errorDescription, validatedURL) => {
      console.error("❌ Failed to load:", errorDescription, validatedURL);
    }
  );

  // Enhanced splash screen control
  let isMainReady = false;
  let isPythonReady = false;

  // Start Python server immediately
  console.log("🚀 Starting Python server...");
  startPythonServer()
    .then((result) => {
      if (result.success) {
        console.log("✅ Python server started successfully");
        isPythonReady = true;
        if (isMainReady) {
          showMainAndCloseSplash();
        }
      } else {
        // PYTHON FAILED - CLOSE SPLASH AND APP
        console.error("❌ Failed to start Python server:", result.error);
        // Close splash and quit app
        app.quit();
        return; // Don't continue
      }
    })
    .catch((error) => {
      // UNEXPECTED ERROR - CLOSE SPLASH AND APP
      console.error("❌ Unexpected error starting Python server:", error);

      app.quit();
    });

  win.once("ready-to-show", () => {
    isMainReady = true;
    if (isPythonReady) {
      showMainAndCloseSplash();
    }
  });

  function showMainAndCloseSplash() {
    if (splashWindow) {
      splashWindow.close();
    }
    win.maximize();
    win.show();
  }

  const menuTemplate = [
    {
      label: app.name,
      submenu: [{ role: "about" }, { type: "separator" }, { role: "quit" }],
    },
    {
      label: "File",
      submenu: [
        {
          label: "New project",
          accelerator: "CmdOrCtrl+N",
          click: async () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (!focusedWindow) return;

            // Reset project path and update title
            currentProjectPath = null;

            // IMPORTANT: Reset the save tracking variables
            isProjectFolder = null;
            lastSavedProjectPath = null;
            lastSavedVideoHash = null;

            updateWindowTitle(null);

            focusedWindow.webContents.send("clear-project");
          },
        },
        {
          label: "Open project",
          accelerator: "CmdOrCtrl+O",
          click: async () => {
            try {
              const focusedWindow = BrowserWindow.getFocusedWindow();
              if (!focusedWindow) return;

              // Check if there are unsaved changes
              const hasUnsaved =
                await focusedWindow.webContents.executeJavaScript(
                  "window.__hasUnsavedProject__ || false"
                );

              if (hasUnsaved) {
                const { response } = await dialog.showMessageBox(
                  focusedWindow,
                  {
                    type: "question",
                    buttons: ["Cancel", "Don't Save", "Save"],
                    defaultId: 2,
                    cancelId: 0,
                    message:
                      "Do you want to save the current project before opening a new one?",
                  }
                );

                if (response === 0) {
                  // Cancel
                  return;
                } else if (response === 2) {
                  // Save first, then open
                  focusedWindow.webContents.send("save-project");
                  await new Promise((resolve) => setTimeout(resolve, 500));
                }
                // For "Don't Save" (response === 1), continue with opening
              }

              // FIXED: Show the file dialog here in main process, then send the selected path
              const result = await dialog.showOpenDialog({
                title: "Open Project",
                filters: [
                  { name: "Plank Projects", extensions: ["plank"] },
                  { name: "All Files", extensions: ["*"] },
                ],
                properties: ["openFile"],
              });

              if (!result.canceled && result.filePaths.length > 0) {
                const selectedPath = result.filePaths[0];

                // Send the selected file path to the renderer process
                // This will trigger your existing handleOpenProject function
                focusedWindow.webContents.send(
                  "menu-open-project",
                  selectedPath
                );

                // Update project tracking
                const projectFolder = path.dirname(selectedPath);
                isProjectFolder = projectFolder;
                lastSavedProjectPath = projectFolder;
                console.log("📂 Menu selected project path:", selectedPath);
              }
            } catch (error) {
              console.error("❌ Menu open project failed:", error);
            }
          },
        },
        {
          label: "Save",
          accelerator: "CmdOrCtrl+S",
          id: "saveMenuItem",
          enabled: false,
          click: () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (focusedWindow) {
              focusedWindow.webContents.send("save-project");
            }
          },
        },
        {
          label: "Save as",
          accelerator: "CmdOrCtrl+Shift+S",
          id: "saveAsMenu",
          enabled: false,
          click: () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (focusedWindow) {
              focusedWindow.webContents.send("save-project-as");
            }
          },
        },
        { type: "separator" },
        {
          label: "Import media",
          click: () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (focusedWindow) {
              focusedWindow.webContents.send("import-media");
            }
          },
        },
        { label: "Import..." },
        {
          label: "Export markers...",
          click: () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (focusedWindow) {
              focusedWindow.webContents.send("export-markers");
            }
          },
        },
        {
          label: "Export...",
          click: () => {
            const focusedWindow = BrowserWindow.getFocusedWindow();
            if (focusedWindow) {
              focusedWindow.webContents.send("export-media");
            }
          },
        },
      ],
    },
    {
      label: "Edit",
      submenu: [
        { label: "Add cut marker" },
        { label: "Select cut marker" },
        { label: "Remove cut" },
        { label: "Select next cut" },
        { label: "Select previous cut" },
        { label: "Nudge cut point forward 1 fr" },
        { label: "Nudge cut point backward 1 fr" },
      ],
    },
    {
      label: "Options",
      submenu: [
        {
          label: "Character Options",
          submenu: [
            { label: "Select next character" },
            { label: "Character size" },
          ],
        },
        {
          label: "Modifiers",
          submenu: [
            { label: "Environment modifier" },
            { label: "Materials modifier" },
            { label: "Foot wear modifier" },
          ],
        },
      ],
    },
    {
      label: "Help",
      submenu: [
        {
          label: "Learn More",
          click: async () => {
            await shell.openExternal("https://example.com");
          },
        },
        { role: "toggledevtools" },
      ],
    },
  ];

  const menu = Menu.buildFromTemplate(menuTemplate);

  Menu.setApplicationMenu(menu);
  menuItemSave = menu.getMenuItemById("saveMenuItem");
  menuItemSaveAs = menu.getMenuItemById("saveAsMenu");
  return win;
}

function updateWindowTitle(projectName = null) {
  if (!mainWindow) return;

  currentProjectName = projectName || "Untitled";
  const title = `MCL Soundpost One - ${currentProjectName}`;
  mainWindow.setTitle(title);

  console.log(`✅ Window title updated to: ${title}`);
}
ipcMain.handle("clear-project", async () => {
  // Reset all project tracking variables
  currentProjectPath = null;
  isProjectFolder = null;
  lastSavedProjectPath = null;
  lastSavedVideoHash = null;

  updateWindowTitle(null);
  return { success: true };
});
// Enable menu save
ipcMain.on("enable-save-menu", (event, isEnabled) => {
  if (menuItemSave && menuItemSaveAs) {
    menuItemSave.enabled = isEnabled;
    menuItemSaveAs.enabled = isEnabled;
  }
  console.log(`Save Menu is now ${isEnabled ? "enabled" : "disabled"}`);
});

const saveProjectToFile = async (
  projectData,
  filePath = null,
  videoFile = null
) => {
  try {
    let savePath = filePath || currentProjectPath;

    if (!savePath) {
      const result = await dialog.showSaveDialog({
        title: "Save Project",
        defaultPath: "Untitled.plank",
        filters: [
          { name: "Plank Projects", extensions: ["plank"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      if (result.canceled) {
        return { success: false, canceled: true };
      }

      savePath = result.filePath;
    }

    // Ensure .plank extension
    if (!savePath.endsWith(".plank")) {
      savePath += ".plank";
    }

    // Extract project name from file path
    const projectName = path.basename(savePath, ".plank");

    // ... rest of your save logic ...

    // Update current project path
    currentProjectPath = savePath;

    // Update window title with project name
    updateWindowTitle(projectName);

    return {
      success: true,
      filePath: savePath,
      fileName: path.basename(savePath),
      projectName: projectName,
      // ... rest of your return object
    };
  } catch (error) {
    console.error("❌ SAVE FAILED:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};
// IPC Handlers
let isProjectFolder = null;
let lastSavedVideoHash = null;
let lastSavedProjectPath = null;

ipcMain.handle(
  "save-project",
  async (event, videoPath, buffer, videoHash, aiVideoAnalytics) => {
    try {
      console.log("📥 Save project IPC called");
      console.log("📊 Current state:", {
        hasCurrentProjectPath: !!currentProjectPath,
        hasLastSavedProjectPath: !!lastSavedProjectPath,
        isProjectFolder: !!isProjectFolder,
        lastSavedVideoHash: lastSavedVideoHash,
        currentVideoHash: videoHash,
      });

      // Handle different buffer types safely
      let safeBuffer;
      if (Buffer.isBuffer(buffer)) {
        safeBuffer = buffer;
      } else if (buffer instanceof Uint8Array) {
        safeBuffer = Buffer.from(buffer);
      } else if (buffer instanceof ArrayBuffer) {
        safeBuffer = Buffer.from(buffer);
      } else if (Array.isArray(buffer)) {
        safeBuffer = Buffer.from(buffer);
      } else {
        throw new Error(`Unsupported buffer type: ${buffer.constructor.name}`);
      }

      // Determine if we need to show save dialog
      let needsDialog = false;

      // Show dialog if:
      // 1. No current project path (new project)
      // 2. No last saved path (never saved before)
      // 3. Video hash changed significantly (different video)
      if (!currentProjectPath || !lastSavedProjectPath) {
        console.log("📝 Showing dialog: No existing project path");
        needsDialog = true;
      } else if (videoHash !== lastSavedVideoHash) {
        console.log("📝 Showing dialog: Video changed");
        needsDialog = true;
      }

      let projectFolder;

      if (needsDialog) {
        // Show save dialog for new project or changed video
        const baseSavePath = path.join(
          app.getPath("userData"),
          "saved_projects"
        );
        fs.mkdirSync(baseSavePath, { recursive: true });

        const { canceled, filePath } = await dialog.showSaveDialog({
          title: "Save Project",
          defaultPath: path.join(baseSavePath, "my-video-project"),
          showsTagField: false,
          filters: [
            { name: "Plank Projects", extensions: ["*"] },
            { name: "All Files", extensions: ["*"] },
          ],
        });

        if (canceled || !filePath) {
          return { success: false, error: "User canceled" };
        }

        projectFolder = filePath.replace(/\.plank$/i, "");
        fs.mkdirSync(projectFolder, { recursive: true });

        // Update tracking variables
        currentProjectPath = projectFolder;
        lastSavedProjectPath = projectFolder;
        lastSavedVideoHash = videoHash;
        isProjectFolder = null; // Reset this
      } else {
        // Use existing project folder
        projectFolder = lastSavedProjectPath;
        console.log("📁 Using existing project folder:", projectFolder);
      }

      const folderName = path.basename(projectFolder);
      const videoFilename = `${folderName}.mp4`;
      const videoTargetPath = path.join(projectFolder, videoFilename);

      // Save video
      fs.writeFileSync(videoTargetPath, safeBuffer);
      console.log("✅ Video file saved to:", videoTargetPath);

      // Save metadata
      const plankFilePath = path.join(projectFolder, `${folderName}.plank`);
      const projectData = {
        video: videoFilename,
        savedPath: videoFilename,
        saveDir: projectFolder,
        savedAt: new Date().toISOString(),
        segments: {
          aiVideoAnalytics: aiVideoAnalytics || {},
        },
      };

      fs.writeFileSync(plankFilePath, JSON.stringify(projectData, null, 2));
      console.log("✅ Project metadata saved");

      // Update window title and current project path
      currentProjectPath = plankFilePath;
      updateWindowTitle(folderName);

      return { success: true, path: projectFolder, data: projectData };
    } catch (err) {
      console.error("❌ Save project failed:", err);
      return { success: false, error: err.message };
    }
  }
);

ipcMain.handle(
  "open-project-file",
  async (event, skipDialog = false, filePath = null) => {
    try {
      let selectedPath = filePath;

      // Only show dialog if not called from menu
      if (!skipDialog && !selectedPath) {
        const { canceled, filePaths } = await dialog.showOpenDialog({
          title: "Open Project",
          filters: [
            { name: "Plank Projects", extensions: ["plank"] },
            { name: "All Files", extensions: ["*"] },
          ],
          properties: ["openFile"],
        });

        if (canceled || filePaths.length === 0) {
          return { success: false, error: "User canceled" };
        }

        selectedPath = filePaths[0];
      }

      if (!selectedPath) {
        return { success: false, error: "No file path provided" };
      }

      const projectFolder = path.dirname(selectedPath);
      const folderName = path.basename(projectFolder);

      console.log(`📂 Opening project from: ${projectFolder}`);
      console.log(`📄 .plank file: ${selectedPath}`);

      // Read the .plank file metadata
      const metadata = JSON.parse(fs.readFileSync(selectedPath, "utf-8"));
      console.log("📊 Project metadata:", metadata);

      // ... rest of your enhanced openProjectFile logic ...

      return {
        success: true,
        metadata,
        projectPath: projectFolder,
        videoPath,
        segmentsToRecreate,
        audioFilesToProcess,
        projectName: folderName,
      };
    } catch (err) {
      console.error("❌ Open project failed:", err);
      return { success: false, error: err.message };
    }
  }
);

ipcMain.handle("save-project-as", async (event, videoPath, buffer) => {
  try {
    console.log("💾 Save As project IPC called");

    // Handle buffer conversion (same as regular save)
    let safeBuffer;
    if (Buffer.isBuffer(buffer)) {
      safeBuffer = buffer;
    } else if (buffer instanceof Uint8Array) {
      safeBuffer = Buffer.from(buffer);
    } else if (buffer instanceof ArrayBuffer) {
      safeBuffer = Buffer.from(buffer);
    } else if (Array.isArray(buffer)) {
      safeBuffer = Buffer.from(buffer);
    } else {
      throw new Error(`Unsupported buffer type: ${buffer.constructor.name}`);
    }

    const baseSavePath = path.join(app.getPath("userData"), "saved_projects");
    fs.mkdirSync(baseSavePath, { recursive: true });

    // ALWAYS show dialog for Save As
    const { canceled, filePath } = await dialog.showSaveDialog({
      title: "Save Project As",
      defaultPath: path.join(baseSavePath, "my-video-project"),
      showsTagField: false,
      filters: [
        { name: "Plank Projects", extensions: ["*"] },
        { name: "All Files", extensions: ["*"] },
      ],
    });

    if (canceled || !filePath) {
      return { success: false, error: "User canceled" };
    }

    const projectFolder = filePath.replace(/\.plank$/i, "");
    fs.mkdirSync(projectFolder, { recursive: true });

    // Save video
    const folderName = path.basename(projectFolder);
    const videoFilename = `${folderName}.mp4`;
    const targetVideoPath = path.join(projectFolder, videoFilename);
    fs.writeFileSync(targetVideoPath, safeBuffer);

    // Save metadata
    const plankFilePath = path.join(projectFolder, `${folderName}.plank`);
    const projectData = {
      video: videoFilename,
      savedPath: videoFilename,
      saveDir: projectFolder,
      savedAt: new Date().toISOString(),
      segments: {
        aiVideoAnalytics: {},
      },
    };
    fs.writeFileSync(plankFilePath, JSON.stringify(projectData, null, 2));

    // IMPORTANT: Update tracking variables for Save As
    currentProjectPath = plankFilePath;
    lastSavedProjectPath = projectFolder;
    // Generate hash for the new video
    const hashData = safeBuffer.slice(0, Math.min(1024, safeBuffer.length));
    lastSavedVideoHash = Buffer.from(hashData).toString("base64");
    isProjectFolder = null;

    // Update window title with new project name
    updateWindowTitle(folderName);

    console.log("✅ Save As completed and title updated:", folderName);

    return { success: true, path: projectFolder, data: projectData };
  } catch (err) {
    console.error("❌ Save As failed:", err);
    return { success: false, error: err.message };
  }
});
ipcMain.handle(
  "update-project-tracking",
  async (event, projectPath, projectName) => {
    try {
      const projectFolder = path.dirname(projectPath);

      // Update all tracking variables
      currentProjectPath = projectPath;
      lastSavedProjectPath = projectFolder;
      isProjectFolder = null;

      // Update window title
      updateWindowTitle(projectName);

      console.log("✅ Project tracking updated:", { projectPath, projectName });

      return { success: true };
    } catch (error) {
      console.error("❌ Failed to update project tracking:", error);
      return { success: false, error: error.message };
    }
  }
);
ipcMain.handle("new-project", async () => {
  const { canceled, filePaths } = await dialog.showOpenDialog({
    filters: [
      { name: "Video Files", extensions: ["mp4", "mov", "avi", "mkv", "webm"] },
    ],
    properties: ["openFile"],
  });
  if (canceled) return null;
  return filePaths[0];
});

// File reading handler for buffer operations (SINGLE DEFINITION)
ipcMain.handle("file:readAsBuffer", async (event, filePath) => {
  try {
    if (!filePath || typeof filePath !== "string") {
      throw new Error(
        `Invalid filePath parameter: ${filePath} (type: ${typeof filePath})`
      );
    }

    // Use fsPromises for proper async/await support
    const buffer = await fsPromises.readFile(filePath);
    return buffer;
  } catch (error) {
    console.error("❌ Failed to read file as buffer:", error);
    console.error("❌ FilePath received:", filePath);
    throw error;
  }
});

// File dialog handler (SINGLE DEFINITION)
ipcMain.handle("dialog:openFile", async () => {
  try {
    const result = await dialog.showOpenDialog({
      properties: ["openFile"],
      filters: [
        {
          name: "Video Files",
          extensions: ["mp4", "mov", "avi", "mkv", "webm"],
        },
      ],
    });

    if (result.canceled || result.filePaths.length === 0) {
      return null;
    }

    return result.filePaths[0];
  } catch (error) {
    console.error("❌ File dialog failed:", error);
    throw error;
  }
});

// Copy video file handler (SINGLE DEFINITION)
ipcMain.handle("copy-video-file", async (event, fileUrl) => {
  try {
    const filePath = fileUrl.replace("file://", "");
    const fileName = path.basename(filePath);
    const destDir = path.join(app.getPath("videos"), "saved_projects");

    // Create directory if it doesn't exist
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }

    const destPath = path.join(destDir, fileName);
    fs.copyFileSync(filePath, destPath);

    return { success: true, path: destPath };
  } catch (error) {
    console.error("❌ Copy video file failed:", error);
    return { success: false, error: error.message };
  }
});

// Open project handler
ipcMain.handle("open-project", async () => {
  try {
    const result = await dialog.showOpenDialog({
      title: "Open Project",
      filters: [
        { name: "Plank Projects", extensions: ["plank"] },
        { name: "All Files", extensions: ["*"] },
      ],
      properties: ["openFile"],
    });

    if (result.canceled) {
      return { success: false, canceled: true };
    }

    const filePath = result.filePaths[0];
    // ... your existing open logic ...

    currentProjectPath = filePath;

    // Extract and update project name
    const projectName = path.basename(filePath, ".plank");
    updateWindowTitle(projectName);

    return {
      success: true,
      projectData,
      filePath,
      fileName: path.basename(filePath),
      projectName: projectName,
      // ... rest of your return object
    };
  } catch (error) {
    console.error("❌ OPEN PROJECT FAILED:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

ipcMain.handle("update-window-title", async (event, projectName) => {
  updateWindowTitle(projectName);
  return { success: true };
});

ipcMain.handle("get-project-info", async () => {
  return {
    currentPath: currentProjectPath,
    fileName: currentProjectPath ? path.basename(currentProjectPath) : null,
  };
});

// Clean up on app quit
app.on("before-quit", () => {
  console.log("🛑 App quitting, stopping Python server...");
  stopPythonServer();
});

app.whenReady().then(() => {
  console.log("🚀 Electron app ready");
  createSplashWindow();
  setTimeout(() => {
    createWindow();
  }, 100);
});

app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createSplashWindow();
    createWindow();
  }
});

ipcMain.handle("file-exists", async (event, filePath) => {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error("Error checking file existence:", error);
    return false;
  }
});
ipcMain.handle("file:readFile", async (event, filePath, encoding = "utf8") => {
  try {
    if (!filePath || typeof filePath !== "string") {
      throw new Error(`Invalid filePath parameter: ${filePath}`);
    }
    return await fsPromises.readFile(filePath, encoding);
  } catch (error) {
    console.error("❌ Failed to read file:", error);
    throw error;
  }
});

// Add new IPC handler for reading file as buffer (if not already exists)
ipcMain.handle("file:readFileBuffer", async (event, filePath) => {
  try {
    if (!filePath || typeof filePath !== "string") {
      throw new Error(`Invalid filePath parameter: ${filePath}`);
    }
    const buffer = await fsPromises.readFile(filePath);
    return buffer;
  } catch (error) {
    console.error("❌ Failed to read file as buffer:", error);
    throw error;
  }
});
ipcMain.handle("file:writeFile", async (event, filePath, data) => {
  try {
    if (!filePath || typeof filePath !== "string") {
      throw new Error(`Invalid filePath parameter: ${filePath}`);
    }
    await fsPromises.writeFile(filePath, data);
    return { success: true };
  } catch (error) {
    console.error("❌ Failed to write file:", error);
    throw error;
  }
});
ipcMain.handle("file:directoryExists", async (event, dirPath) => {
  try {
    const stats = await fsPromises.stat(dirPath);
    return stats.isDirectory();
  } catch (error) {
    return false;
  }
});

ipcMain.handle("file:listFiles", async (event, dirPath) => {
  try {
    const files = await fsPromises.readdir(dirPath);
    return files;
  } catch (error) {
    console.error("❌ Failed to list files:", error);
    return [];
  }
});
ipcMain.handle("get-window-title", async () => {
  if (!mainWindow) return null;
  return {
    title: mainWindow.getTitle(),
    projectName: currentProjectName,
    projectPath: currentProjectPath,
  };
});
