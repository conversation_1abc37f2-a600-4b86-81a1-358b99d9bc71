/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
import { execSync } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

const MMAUDIO_DIR = 'electron/python-scripts/mmaudio';
const REPO_URL = 'https://github.com/hkchengrex/mmaudio.git';

console.log('🔍 Checking for existing MMAudio repository...');

if (existsSync(MMAUDIO_DIR)) {
  console.log('✅ MMAudio repository already exists, skipping clone...');
  console.log(`📁 Location: ${path.resolve(MMAUDIO_DIR)}`);
  process.exit(0);
}

console.log('📥 Cloning MMAudio repository...');

try {
  execSync(`git clone ${REPO_URL} ${MMAUDIO_DIR}`, { stdio: 'inherit' });
  console.log('✅ MMAudio repository cloned successfully!');
  console.log(`📁 Location: ${path.resolve(MMAUDIO_DIR)}`);
} catch (error) {
  console.error('❌ Failed to clone MMAudio repository:', error.message);
  process.exit(1);
}