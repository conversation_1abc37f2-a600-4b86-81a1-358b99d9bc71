"use client";

// UI Components
import { SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "./components/sidebar/app-sidebar";
import { Editor } from "./components/core/editor";
import { SidebarProvider as UISidebarProvider } from "@/components/ui/sidebar";
import { SidebarProvider as EditorSidebarProvider } from "./contexts/sidebar-context";

// Context Providers
import { EditorProvider } from "./contexts/editor-context"; // Your existing context
import AppProvider from "./contexts/AppProvider";
// Custom Hooks
import { useOverlays } from "./hooks/use-overlays";
import { useVideoPlayer } from "./hooks/use-video-player";
import { useTimelineClick } from "./hooks/use-timeline-click";
import { useAspectRatio } from "./hooks/use-aspect-ratio";
import { useCompositionDuration } from "./hooks/use-composition-duration";
import { useHistory } from "./hooks/use-history";

// Types
import {
  Overlay,
  OverlayType,
  ClipOverlay,
  SoundOverlay,
  ImageOverlay,
} from "./types";
import { useRendering } from "./hooks/use-rendering";
import { AUTO_SAVE_INTERVAL, FPS, RENDER_TYPE } from "./constants";
import { TimelineProvider } from "./contexts/timeline-context";

// Components
import { AutosaveStatus } from "./components/autosave/autosave-status";
import { useState, useEffect, useCallback } from "react";
import { useAutosave } from "./hooks/use-autosave";
import { LocalMediaProvider } from "./contexts/local-media-context";
import { KeyframeProvider } from "./contexts/keyframe-context";
import { AssetLoadingProvider } from "./contexts/asset-loading-context";
import { DragDropWrapper } from "./components/core/drag-drop-wrapper";

// File processing functionality (integrate with your existing context)
const useFileProcessing = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);

  const createVideoOverlay = useCallback(
    async (file: File): Promise<ClipOverlay> => {
      return new Promise((resolve) => {
        const video = document.createElement("video");
        const url = URL.createObjectURL(file);

        video.onloadedmetadata = () => {
          const duration = video.duration;
          const durationInFrames = Math.floor(duration * 30); // Assuming 30 FPS

          const overlay: ClipOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.VIDEO, // Using enum instead of string
            content: file.name,
            src: url,
            durationInFrames,
            from: 0,
            height: video.videoHeight || 1080,
            width: video.videoWidth || 1920,
            row: 0,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            videoStartTime: 0,
            speed: 1,
            styles: {
              opacity: 1,
              zIndex: 1,
              objectFit: "contain",
              objectPosition: "center",
              volume: 1,
              borderRadius: "0px",
              filter: "none",
            },
          };

          resolve(overlay);
        };

        video.onerror = () => {
          // Fallback for unsupported video formats
          const overlay: ClipOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.VIDEO, // Using enum instead of string
            content: file.name,
            src: url,
            durationInFrames: 900, // Default 30 seconds at 30 FPS
            from: 0,
            height: 1080,
            width: 1920,
            row: 0,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            videoStartTime: 0,
            speed: 1,
            styles: {
              opacity: 1,
              zIndex: 1,
              objectFit: "contain",
              objectPosition: "center",
              volume: 1,
              borderRadius: "0px",
              filter: "none",
            },
          };

          resolve(overlay);
        };

        video.src = url;
      });
    },
    []
  );

  const createAudioOverlay = useCallback(
    async (file: File): Promise<SoundOverlay> => {
      return new Promise((resolve) => {
        const audio = document.createElement("audio");
        const url = URL.createObjectURL(file);

        audio.onloadedmetadata = () => {
          const duration = audio.duration;
          const durationInFrames = Math.floor(duration * 30);

          const overlay: SoundOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.SOUND, // Using enum instead of string
            content: file.name,
            src: url,
            durationInFrames,
            from: 0,
            height: 0, // Audio has no visual height
            width: 0, // Audio has no visual width
            row: 1, // Audio on second row
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            startFromSound: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              volume: 1,
            },
          };

          resolve(overlay);
        };

        audio.onerror = () => {
          const overlay: SoundOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.SOUND, // Using enum instead of string
            content: file.name,
            src: url,
            durationInFrames: 900,
            from: 0,
            height: 0,
            width: 0,
            row: 1,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            startFromSound: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              volume: 1,
            },
          };

          resolve(overlay);
        };

        audio.src = url;
      });
    },
    []
  );

  const createImageOverlay = useCallback(
    async (file: File): Promise<ImageOverlay> => {
      return new Promise((resolve) => {
        const img = document.createElement("img");
        const url = URL.createObjectURL(file);

        img.onload = () => {
          const overlay: ImageOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.IMAGE, // Using enum instead of string
            src: url,
            content: file.name,
            durationInFrames: 150, // Default 5 seconds at 30 FPS
            from: 0,
            height: img.naturalHeight,
            width: img.naturalWidth,
            row: 2, // Images on third row
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              filter: "none",
              borderRadius: "0px",
              objectFit: "contain",
              objectPosition: "center",
              boxShadow: "none",
              border: "none",
            },
          };

          resolve(overlay);
        };

        img.onerror = () => {
          const overlay: ImageOverlay = {
            id: Math.floor(Date.now() + Math.random() * 1000),
            type: OverlayType.IMAGE, // Using enum instead of string
            src: url,
            content: file.name,
            durationInFrames: 150,
            from: 0,
            height: 1080,
            width: 1920,
            row: 2,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            styles: {
              opacity: 1,
              zIndex: 1,
              filter: "none",
              borderRadius: "0px",
              objectFit: "contain",
              objectPosition: "center",
              boxShadow: "none",
              border: "none",
            },
          };

          resolve(overlay);
        };

        img.src = url;
      });
    },
    []
  );

  const processFiles = useCallback(
    async (files: File[]): Promise<Overlay[]> => {
      setIsProcessing(true);
      setProcessingProgress(0);

      const overlays: Overlay[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const extension = "." + file.name.split(".").pop()?.toLowerCase();

        try {
          let overlay: Overlay;

          if (
            [".mp4", ".mov", ".avi", ".mkv", ".webm", ".m4v"].includes(
              extension
            )
          ) {
            overlay = await createVideoOverlay(file);
          } else if (
            [".mp3", ".wav", ".aac", ".m4a", ".ogg", ".flac"].includes(
              extension
            )
          ) {
            overlay = await createAudioOverlay(file);
          } else if (
            [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"].includes(
              extension
            )
          ) {
            overlay = await createImageOverlay(file);
          } else {
            continue; // Skip unsupported files
          }

          overlays.push(overlay);
          setProcessingProgress(((i + 1) / files.length) * 100);
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
        }
      }

      setIsProcessing(false);
      setProcessingProgress(0);

      return overlays;
    },
    [createVideoOverlay, createAudioOverlay, createImageOverlay]
  );

  return {
    processFiles,
    isProcessing,
    processingProgress,
  };
};
const generateVideoThumbnail = (
  videoElement: HTMLVideoElement,
  timeInSeconds: number = 0
): string => {
  try {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) return "";

    // Set thumbnail size
    canvas.width = 160;
    canvas.height = 90;

    // Set video time and draw frame
    videoElement.currentTime = timeInSeconds;
    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

    // Return as data URL
    return canvas.toDataURL("image/jpeg", 0.7);
  } catch (error) {
    console.warn("Failed to generate thumbnail:", error);
    return "";
  }
};
export default function ReactVideoEditor({ projectId }: { projectId: string }) {
  // Autosave state
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState<number | null>(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);

  // File processing hook
  const { processFiles, isProcessing, processingProgress } =
    useFileProcessing();

  // Overlay management hooks - Initialize with empty array for clean start
  const {
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    deleteOverlaysByRow,
    updateOverlayStyles,
    resetOverlays,
  } = useOverlays([]); // Empty default state

  // Video player controls and state
  const { isPlaying, currentFrame, playerRef, togglePlayPause, formatTime } =
    useVideoPlayer();

  // Composition duration calculations
  const { durationInFrames, durationInSeconds } =
    useCompositionDuration(overlays);

  // Aspect ratio and player dimension management
  const {
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    scaleFactor,
    updateScaleFactor,
    getScaleFactor,
  } = useAspectRatio();

  // Smart file handling with positioning logic
  const handleFilesAdded = useCallback(
    async (files: File[]) => {
      if (files.length === 0) return;

      try {
        // Process files one by one to add to timeline
        for (const file of files) {
          const url = URL.createObjectURL(file);
          const extension = "." + file.name.split(".").pop()?.toLowerCase();

          // Determine file type
          let fileType: "video" | "audio" | "image" | null = null;
          if (
            [".mp4", ".mov", ".avi", ".mkv", ".webm", ".m4v"].includes(
              extension
            )
          ) {
            fileType = "video";
          } else if (
            [".mp3", ".wav", ".aac", ".m4a", ".ogg", ".flac"].includes(
              extension
            )
          ) {
            fileType = "audio";
          } else if (
            [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"].includes(
              extension
            )
          ) {
            fileType = "image";
          }

          if (!fileType) continue; // Skip unsupported files

          // Get metadata and create overlay using your existing approach
          const { width, height } = getAspectRatioDimensions();

          // Find next available position (simple version)
          const existingOverlays = overlays.filter((o) => o.row === 0); // Use row 0 for simplicity
          const lastOverlay =
            existingOverlays.length > 0
              ? existingOverlays.sort(
                  (a, b) =>
                    b.from + b.durationInFrames - (a.from + a.durationInFrames)
                )[0]
              : null;
          const from = lastOverlay
            ? lastOverlay.from + lastOverlay.durationInFrames + 5
            : 0;

          if (fileType === "video") {
            const video = document.createElement("video");
            video.onloadedmetadata = () => {
              const newOverlay: ClipOverlay = {
                left: 0,
                top: 0,
                width,
                height,
                durationInFrames: Math.round(video.duration * FPS),
                from,
                id: Date.now() + Math.random() * 1000,
                rotation: 0,
                row: 0,
                isDragging: false,
                type: OverlayType.VIDEO,
                content: file.name,
                src: url,
                videoStartTime: 0, // IMPORTANT: Always start at 0
                speed: 1,
                styles: {
                  opacity: 1,
                  zIndex: 100,
                  objectFit: "contain",
                  objectPosition: "center",
                  volume: 1,
                },
              };
              addOverlay(newOverlay);
            };
            video.onerror = () => {
              // Fallback with default duration
              const newOverlay: ClipOverlay = {
                left: 0,
                top: 0,
                width,
                height,
                durationInFrames: 300, // 10 seconds default
                from,
                id: Date.now() + Math.random() * 1000,
                rotation: 0,
                row: 0,
                isDragging: false,
                type: OverlayType.VIDEO,
                content: file.name,
                src: url,
                videoStartTime: 0,
                speed: 1,
                styles: {
                  opacity: 1,
                  zIndex: 100,
                  objectFit: "contain",
                  objectPosition: "center",
                  volume: 1,
                },
              };
              addOverlay(newOverlay);
            };
            video.src = url;
          } else if (fileType === "audio") {
            const audio = document.createElement("audio");
            audio.onloadedmetadata = () => {
              const newOverlay: SoundOverlay = {
                left: 0,
                top: 0,
                width: 0,
                height: 0,
                durationInFrames: Math.round(audio.duration * FPS),
                from,
                id: Date.now() + Math.random() * 1000,
                rotation: 0,
                row: 1, // Audio on row 1
                isDragging: false,
                type: OverlayType.SOUND,
                content: file.name,
                src: url,
                startFromSound: 0, // IMPORTANT: Always start at 0
                styles: {
                  volume: 1,
                },
              };
              addOverlay(newOverlay);
            };
            audio.onerror = () => {
              const newOverlay: SoundOverlay = {
                left: 0,
                top: 0,
                width: 0,
                height: 0,
                durationInFrames: 300,
                from,
                id: Date.now() + Math.random() * 1000,
                rotation: 0,
                row: 1,
                isDragging: false,
                type: OverlayType.SOUND,
                content: file.name,
                src: url,
                startFromSound: 0,
                styles: {
                  volume: 1,
                },
              };
              addOverlay(newOverlay);
            };
            audio.src = url;
          } else if (fileType === "image") {
            const newOverlay: ImageOverlay = {
              left: 0,
              top: 0,
              width,
              height,
              durationInFrames: 150, // 5 seconds default
              from,
              id: Date.now() + Math.random() * 1000,
              rotation: 0,
              row: 2, // Images on row 2
              isDragging: false,
              type: OverlayType.IMAGE,
              src: url,
              content: file.name,
              styles: {
                opacity: 1,
                objectFit: "contain",
                objectPosition: "center",
              },
            };
            addOverlay(newOverlay);
          }
        }

        console.log(`Successfully processed ${files.length} files`);
      } catch (error) {
        console.error("Error processing files:", error);
      }
    },
    [overlays, getAspectRatioDimensions, addOverlay]
  );
  // Event handlers
  const handleOverlayChange = (updatedOverlay: Overlay) => {
    changeOverlay(updatedOverlay.id, () => updatedOverlay);
  };

  const { width: compositionWidth, height: compositionHeight } =
    getAspectRatioDimensions();

  const handleTimelineClick = useTimelineClick(playerRef, durationInFrames);

  const inputProps = {
    overlays,
    durationInFrames: Math.max(durationInFrames, 900), // Minimum 30 seconds
    fps: FPS,
    width: compositionWidth,
    height: compositionHeight,
    src: "",
  };

  const { renderMedia, state } = useRendering(
    "TestComponent",
    inputProps,
    RENDER_TYPE
  );

  // History management
  const { undo, redo, canUndo, canRedo } = useHistory(overlays, setOverlays);

  // Create the editor state object to be saved
  const editorState = {
    overlays,
    aspectRatio,
    playerDimensions,
  };

  // Autosave implementation
  const { saveState, loadState } = useAutosave(projectId, editorState, {
    interval: AUTO_SAVE_INTERVAL,
    onSave: () => {
      setIsSaving(false);
      setLastSaveTime(Date.now());
    },
    onLoad: (loadedState) => {
      console.log("Loading saved state:", loadedState);
      if (loadedState) {
        // Apply loaded state to editor with fallbacks
        setOverlays(loadedState.overlays || []);
        if (loadedState.aspectRatio) setAspectRatio(loadedState.aspectRatio);
        if (loadedState.playerDimensions) {
          updatePlayerDimensions(
            loadedState.playerDimensions.width,
            loadedState.playerDimensions.height
          );
        }
      }
    },
    onAutosaveDetected: (timestamp) => {
      // Handle autosave recovery
      if (!initialLoadComplete) {
        console.log("Autosave detected from:", new Date(timestamp));
      }
    },
  });

  // Mark initial load as complete
  useEffect(() => {
    setInitialLoadComplete(true);
  }, []);

  // Manual save function
  const handleManualSave = useCallback(async () => {
    setIsSaving(true);
    try {
      await saveState();
      console.log("Project saved successfully");
    } catch (error) {
      console.error("Error saving project:", error);
      setIsSaving(false);
    }
  }, [saveState]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        return;
      }

      // Ctrl/Cmd + S for save
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        handleManualSave();
      }

      // Ctrl/Cmd + Z for undo
      if ((e.ctrlKey || e.metaKey) && e.key === "z" && !e.shiftKey) {
        e.preventDefault();
        undo();
      }

      // Ctrl/Cmd + Shift + Z or Ctrl/Cmd + Y for redo
      if (
        (e.ctrlKey || e.metaKey) &&
        (e.key === "y" || (e.key === "z" && e.shiftKey))
      ) {
        e.preventDefault();
        redo();
      }

      // Space for play/pause (only if there are overlays)
      if (e.code === "Space" && overlays.length > 0) {
        e.preventDefault();
        togglePlayPause();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleManualSave, undo, redo, togglePlayPause, overlays.length]);

  // Debug logging
  useEffect(() => {
    console.log("Current overlays:", overlays);
    console.log("Duration in frames:", durationInFrames);
    console.log("Is processing:", isProcessing);
  }, [overlays, durationInFrames, isProcessing]);

  // Combine all editor context values using your existing context structure
  const editorContextValue = {
    // Overlay management
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    handleOverlayChange,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    resetOverlays,
    deleteOverlaysByRow,
    updateOverlayStyles,

    // Player controls
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    handleTimelineClick,
    playbackRate,
    setPlaybackRate,

    // Dimensions and duration
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    scaleFactor,
    updateScaleFactor,
    getScaleFactor,
    durationInFrames: Math.max(durationInFrames, 900), // Ensure minimum duration
    durationInSeconds,

    // Rendering
    renderType: RENDER_TYPE,
    renderMedia,
    state,

    // History management
    undo,
    redo,
    canUndo,
    canRedo,

    // Autosave
    saveProject: handleManualSave,

    // File processing (matching your context interface)
    handleFilesAdded,
    isProcessing,
    processingProgress,
  };

  return (
    <AppProvider>
      <UISidebarProvider>
        <EditorSidebarProvider>
          <KeyframeProvider>
            <TimelineProvider>
              <EditorProvider value={editorContextValue}>
                <LocalMediaProvider>
                  <AssetLoadingProvider>
                    {/* Uncomment to show sidebar */}
                    {/* <AppSidebar /> */}

                    <SidebarInset>
                      <DragDropWrapper onFilesAdded={handleFilesAdded}>
                        <Editor />
                      </DragDropWrapper>
                    </SidebarInset>

                    {/* Status indicators */}
                    <AutosaveStatus
                      isSaving={isSaving}
                      lastSaveTime={lastSaveTime}
                    />

                    {/* File processing indicator */}
                    {isProcessing && (
                      <div className="fixed bottom-4 left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg z-50">
                        <div className="flex items-center gap-3">
                          <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent" />
                          <div className="flex flex-col gap-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              Processing files...
                            </span>
                            <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${Math.round(processingProgress)}%`,
                                }}
                              />
                            </div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {Math.round(processingProgress)}% complete
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Empty state helper */}
                    {overlays.length === 0 && !isProcessing && (
                      <div className="fixed bottom-4 right-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 shadow-lg z-40">
                        <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                          <span className="text-2xl">💡</span>
                          <div className="text-sm">
                            <div className="font-medium">Ready to start!</div>
                            <div className="text-blue-600 dark:text-blue-400">
                              Drag files anywhere to begin
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </AssetLoadingProvider>
                </LocalMediaProvider>
              </EditorProvider>
            </TimelineProvider>
          </KeyframeProvider>
        </EditorSidebarProvider>
      </UISidebarProvider>
    </AppProvider>
  );
}
