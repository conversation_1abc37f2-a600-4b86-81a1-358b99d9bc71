// Updated VideoContainer.jsx
import React from "react";
import VideoDropzone from "./VideoDropzone";
import VideoPreview from "./VideoPreview";
import VideoPlaceholder from "./VideoPlaceholder";

const VideoContainer = ({
  videoSrc,
  videoPlayerProps,
  videoPathProps,
  dragDropProps,
  onVideoFile,
  selectedSegment = null,
  segments = [], // ADD THIS PROP - segments with audio replacement data
}) => {
  return (
    <div
      className={`video-upload-container relative bg-gradient-to-bl from-stone-300/25 via-white/20 to-neutral-400/25 rounded-2xl shadow-[0px_4px_2px_16px_rgba(225,225,225,0.15)] outline-1 outline-white inline-flex flex-col justify-end items-start overflow-hidden ${
        videoSrc
          ? "lg:h-[53vh] xl:h-[57vh] lg:w-[646px] xl:w-[1078px] flex justify-center items-start"
          : "lg:h-[358px] xl:h-[598px] lg:w-[800px] xl:w-[1078px]"
      }`}
    >
      {videoSrc ? (
        // When video exists, render without drag and drop
        <div className="w-full h-full relative">
          <VideoPreview
            videoRef={videoPlayerProps.videoRef}
            videoSrc={videoSrc}
            videoFile={videoPathProps}
            isReplacing={false}
            onTimeUpdate={videoPlayerProps.handleTimeUpdate}
            onVideoLoaded={videoPlayerProps.handleVideoLoaded}
            selectedSegment={selectedSegment}
            // ADD THESE PROPS FOR AUDIO REPLACEMENT:
            segments={segments}
            isPlaying={videoPlayerProps.isPlaying}
            currentTime={videoPlayerProps.currentTime}
          />
        </div>
      ) : (
        // When no video, render with drag and drop
        <VideoDropzone
          dragging={dragDropProps.dragging}
          isReplacing={dragDropProps.isReplacing}
          videoSrc={videoSrc}
          onDragHandlers={dragDropProps}
          onVideoFile={onVideoFile}
        >
          {({ triggerFileInput }) => (
            <VideoPlaceholder onUploadClick={triggerFileInput} />
          )}
        </VideoDropzone>
      )}

      {/* Segment preview overlay */}
      {selectedSegment && videoSrc && (
        <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-2 rounded-md text-sm z-10">
          <div className="font-medium">{selectedSegment.name}</div>
          <div className="text-xs opacity-75">
            {formatTime(selectedSegment.start)} -{" "}
            {formatTime(selectedSegment.end)}
          </div>
          <div className="text-xs opacity-75">
            Duration: {formatTime(selectedSegment.end - selectedSegment.start)}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function for time formatting
const formatTime = (timeInSeconds) => {
  if (!timeInSeconds && timeInSeconds !== 0) return "--:--";
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

export default VideoContainer;
