/* eslint-disable no-unused-vars */
// VideoPlayerWithWaveform.js - Fixed version
import React, { useEffect, useState } from "react";
import VideoSetting from "./VideoSetting";
import VideoTimeline from "./VideoTimeline";
import VideoContainer from "../components/video-container/VideoContainer";
import ImportMediaModal from "./modal/ImportMediaModal";
import ExportMarkersModal from "./modal/ExportMarkersModal";
import ExportModal from "./modal/ExportModal";
import { useVideoPlayer } from "../hooks/useVideoPlayer";
import { useDragAndDrop } from "../hooks/useDragAndDrop";
import "../assets/css/VideoPlayerWithWaveform.css";
import Help from "./Help";
import { useAppContext } from "../hooks/useAppContext";

const VideoPlayerWithWaveform = ({
  initialVideoSrc = null,
  initialVideoFile = null,
}) => {
  const videoPlayerProps = useVideoPlayer(initialVideoSrc, initialVideoFile);
  const [videoPathProps, setVideoPathProps] = useState("");
  const [projectFilePath, setProjectFilePath] = useState(null);
  const [selectedSegment, setSelectedSegment] = useState(null);
  const [segments, setSegments] = useState([]);

  // Enhanced loading states
  const [isLoadingProject, setIsLoadingProject] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState("");

  // New state to store segment settings for VideoSetting component
  const [segmentSettingsCache, setSegmentSettingsCache] = useState({});

  const { page } = useAppContext();

  const handleVideoFile = (file) => {
    videoPlayerProps.setVideoSource(file);
    setVideoPathProps(file);
    // Reset segments when new video is loaded
    setSegments([]);
    setSelectedSegment(null);
    setSegmentSettingsCache({});
  };

  const dragDropProps = useDragAndDrop(
    handleVideoFile,
    !!videoPlayerProps.videoSrc
  );

  const handleSegmentUpdate = (updatedSegment) => {
    console.log("Updating segment from VideoSetting:", updatedSegment);

    // Update the segments array with the modified segment
    setSegments((currentSegments) =>
      currentSegments.map((seg) =>
        seg.id === updatedSegment.id ? updatedSegment : seg
      )
    );

    // Update the selected segment
    setSelectedSegment(updatedSegment);
  };

  // Handle segment selection from timeline
  const handleSegmentSelect = (segment) => {
    console.log("🎯 Segment selected:", segment);
    setSelectedSegment(segment);

    // If segment has original settings, cache them for VideoSetting component
    if (segment && segment.originalSettings) {
      console.log("📋 Caching segment settings:", segment.originalSettings);
      setSegmentSettingsCache((prev) => ({
        ...prev,
        [segment.id]: segment.originalSettings,
      }));
    }

    // If a segment is selected, you might want to jump to its start time
    if (segment && videoPlayerProps.handleSeek) {
      videoPlayerProps.handleSeek(segment.start);
    }
  };

  // Handle segments change from timeline
  const handleSegmentsChange = (newSegments) => {
    console.log("🔄 Segments changed:", newSegments);
    setSegments(newSegments);
  };

  // Enhanced function to recreate segment audio
  const recreateSegmentAudio = async (segment, audioFileName, projectPath) => {
    try {
      console.log(`🔄 Recreating audio for segment: ${segment.name}`);
      setLoadingMessage(`Loading audio for ${segment.name}...`);

      const audioPath = `${projectPath}/ai/${audioFileName}`;
      console.log(`🔗 Full audio path: ${audioPath}`);

      // Check if audio file exists
      const audioExists = await window.electronAPI.fileExists(audioPath);
      if (!audioExists) {
        console.warn(`⚠️ Audio file not found: ${audioPath}`);
        return { success: false, error: "Audio file not found" };
      }

      // Create audio element to load the replacement audio
      let audio;

      if (
        window.electronAPI &&
        typeof window.electronAPI.readFileBuffer === "function"
      ) {
        try {
          console.log("📂 Reading audio file as buffer...");
          const buffer = await window.electronAPI.readFileBuffer(audioPath);
          const blob = new Blob([buffer], { type: "audio/wav" });
          const audioUrl = URL.createObjectURL(blob);
          audio = new Audio(audioUrl);
          console.log("✅ Successfully loaded audio from buffer");
        } catch (fileError) {
          console.warn(
            "⚠️ Failed to read audio file as buffer, trying direct path:",
            fileError
          );
          const fileUrl = audioPath.startsWith("file://")
            ? audioPath
            : `file://${audioPath}`;
          audio = new Audio(fileUrl);
        }
      } else {
        audio = new Audio(audioPath);
      }

      // Wait for audio to load
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error("Audio loading timeout"));
        }, 10000);

        audio.addEventListener("loadeddata", () => {
          clearTimeout(timeout);
          resolve();
        });
        audio.addEventListener("error", (e) => {
          clearTimeout(timeout);
          reject(e);
        });
        audio.load();
      });

      console.log("✅ Audio loaded successfully:", {
        duration: audio.duration,
        src: audio.src,
      });

      // Generate waveform data for visualization
      const segmentDuration = segment.end - segment.start;
      const sampleCount = Math.ceil(segmentDuration * 20);
      const waveformData = [];

      for (let i = 0; i < sampleCount; i++) {
        const time = (i / sampleCount) * segmentDuration;
        const fundamental = Math.sin(time * 2 * Math.PI * 2) * 0.6;
        const harmonic2 = Math.sin(time * 2 * Math.PI * 4) * 0.3;
        const harmonic3 = Math.sin(time * 2 * Math.PI * 6) * 0.2;
        const decay = Math.exp(-time * 0.5);
        const variation = (Math.random() - 0.5) * 0.1;
        const amplitude =
          (fundamental + harmonic2 + harmonic3) * decay + variation;
        waveformData.push(Math.max(-1, Math.min(1, amplitude)));
      }

      // Set up audio for looping playback
      audio.loop = true;
      audio.preload = "auto";

      audio.addEventListener("ended", () => {
        audio.currentTime = 0;
        if (!audio.paused) {
          audio.play();
        }
      });

      // Create updated segment with audio data
      const updatedSegment = {
        ...segment,
        hasReplacedAudio: true,
        audioFile: audioFileName,
        audioPath: audioPath,
        audioElement: audio,
        waveformData: waveformData,
        audioDuration: audio.duration,
        shouldMuteOriginal: true,
        audioOverride: true,
        originalAudioMuted: true,
      };

      console.log(
        `✅ Successfully recreated audio for segment: ${segment.name}`
      );
      return { success: true, updatedSegment };
    } catch (error) {
      console.error(
        `❌ Failed to recreate audio for segment ${segment.name}:`,
        error
      );
      return { success: false, error: error.message };
    }
  };

  // Format time helper
  const formatTime = (timeInSeconds) => {
    if (!timeInSeconds && timeInSeconds !== 0) return "00:00";
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Only handle shortcuts when not typing in input fields
      if (e.target.tagName === "INPUT" || e.target.tagName === "TEXTAREA")
        return;

      switch (e.key.toLowerCase()) {
        case " ":
          e.preventDefault();
          videoPlayerProps.togglePlay?.();
          break;
        case "arrowleft":
          e.preventDefault();
          if (videoPlayerProps.handleSeek) {
            const newTime = Math.max(0, videoPlayerProps.currentTime - 5);
            videoPlayerProps.handleSeek(newTime);
          }
          break;
        case "arrowright":
          e.preventDefault();
          if (videoPlayerProps.handleSeek) {
            const newTime = Math.min(
              videoPlayerProps.duration,
              videoPlayerProps.currentTime + 5
            );
            videoPlayerProps.handleSeek(newTime);
          }
          break;
        case "delete":
          if (selectedSegment) {
            const newSegments = segments.filter(
              (seg) => seg !== selectedSegment
            );
            setSegments(newSegments);
            setSelectedSegment(null);
          }
          break;
        case "e":
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [videoPlayerProps, selectedSegment, segments]);

  useEffect(() => {
    const handleOpenProject = async (event, filePath) => {
      try {
        setIsLoadingProject(true);
        setLoadingMessage("Opening project...");
        console.log("🚀 Enhanced project opening started with path:", filePath);

        // Store the project file path
        setProjectFilePath(filePath);
        // Extract project name and update title immediately
        const projectName = filePath.split(/[\\/]/).pop().replace(".plank", "");
        await window.electronAPI.updateWindowTitle(projectName);

        // Try enhanced loading first
        try {
          console.log("📂 Attempting enhanced project loading...");

          // Read the project file using existing sync method
          const projectData = window.electronAPI.readFile(filePath, "utf8");
          const project = JSON.parse(projectData);
          const projectFolder = filePath.includes("\\")
            ? filePath.substring(0, filePath.lastIndexOf("\\"))
            : filePath.substring(0, filePath.lastIndexOf("/"));

          console.log("📊 Project metadata:", project);
          setLoadingMessage("Loading video...");

          // Check if this is an enhanced project with segments
          if (project.segments && project.segments.aiVideoAnalytics) {
            console.log(
              "🔄 Found enhanced project format, processing segments..."
            );

            // Process enhanced format
            const analytics = project.segments.aiVideoAnalytics;
            const recreatedSegments = [];
            const settingsCache = {};
            const audioFilesToProcess = {};

            // Extract segments from analytics
            Object.keys(analytics).forEach((key) => {
              if (key.startsWith("segment") && analytics[key]) {
                const segmentData = analytics[key];

                const segment = {
                  id: segmentData.segmentId || Date.now() + Math.random(),
                  name: segmentData.segmentName || key,
                  start: segmentData.startTime || 0,
                  end: segmentData.endTime || 0,
                  originalSettings: {
                    characterSize: segmentData.characterSize,
                    weather: segmentData.weather,
                    environment: segmentData.environment,
                    groundTexture: segmentData.groundTexture,
                    groundMaterial: segmentData.groundMaterial,
                    footwear: segmentData.footwear,
                    fullPrompt: segmentData.fullPrompt,
                    settings: segmentData.settings || {},
                  },
                };

                settingsCache[segment.id] = segment.originalSettings;
                recreatedSegments.push(segment);

                if (segmentData.audioFile) {
                  audioFilesToProcess[segment.id] = segmentData.audioFile;
                  console.log(
                    `🎵 Found audio file for segment ${segment.name}: ${segmentData.audioFile}`
                  );
                }
              }
            });

            // Load video
            const videoPath = `${projectFolder}/${project.video}`;
            console.log("📺 Loading video from:", videoPath);

            if (window.electronAPI && window.electronAPI.readFileBuffer) {
              try {
                const buffer = window.electronAPI.readFileBuffer(videoPath);
                const uint8Array = new Uint8Array(buffer);
                const ext = videoPath.toLowerCase().split(".").pop();
                const mimeTypeMap = {
                  mp4: "video/mp4",
                  mov: "video/quicktime",
                  avi: "video/x-msvideo",
                  mkv: "video/x-matroska",
                  webm: "video/webm",
                };
                const mimeType = mimeTypeMap[ext] || "video/mp4";

                const blob = new Blob([uint8Array], { type: mimeType });
                const fileName = videoPath.split(/[\\/]/).pop();
                const file = new File([blob], fileName, { type: mimeType });

                videoPlayerProps.setVideoSource(file);
                setVideoPathProps({ path: videoPath, name: project.video });
              } catch (error) {
                console.warn(
                  "Failed to load video as buffer, trying direct path:",
                  error
                );
                const fileUrl = videoPath.startsWith("file://")
                  ? videoPath
                  : `file://${videoPath}`;
                videoPlayerProps.setVideoSource(fileUrl);
                setVideoPathProps({ path: videoPath, name: project.video });
              }
            }

            // Update segments and cache
            setSegmentSettingsCache(settingsCache);
            setSegments(recreatedSegments);
            console.log("✅ Segments updated in UI");

            // Process audio files if any
            if (Object.keys(audioFilesToProcess).length > 0) {
              setLoadingMessage("Processing audio files...");
              console.log("🎵 Processing audio files...");

              const audioPromises = [];
              for (const [segmentId, audioFileName] of Object.entries(
                audioFilesToProcess
              )) {
                const segment = recreatedSegments.find(
                  (seg) => seg.id == segmentId
                );
                if (segment) {
                  const audioPromise = recreateSegmentAudio(
                    segment,
                    audioFileName,
                    projectFolder
                  )
                    .then((audioResult) => ({
                      segmentId,
                      success: audioResult.success,
                      updatedSegment: audioResult.updatedSegment,
                      error: audioResult.error,
                    }))
                    .catch((error) => ({
                      segmentId,
                      success: false,
                      error: error.message,
                    }));

                  audioPromises.push(audioPromise);
                }
              }

              // Wait for all audio processing
              const audioResults = await Promise.all(audioPromises);

              // Update segments with audio data
              const finalSegments = recreatedSegments.map((segment) => {
                const audioResult = audioResults.find(
                  (result) => result.segmentId == segment.id
                );
                if (audioResult && audioResult.success) {
                  return audioResult.updatedSegment;
                }
                return segment;
              });

              setSegments(finalSegments);

              const successCount = audioResults.filter(
                (result) => result.success
              ).length;
              console.log(
                `🎵 Audio processing complete: ${successCount} success`
              );
            }

            // Select first segment
            if (recreatedSegments.length > 0) {
              handleSegmentSelect(recreatedSegments[0]);
              console.log(
                `📍 Selected first segment: ${recreatedSegments[0].name}`
              );
            }
            return; // Exit early since enhanced loading succeeded
          }
        } catch (enhancedError) {
          console.warn(
            "⚠️ Enhanced loading failed, falling back to legacy:",
            enhancedError
          );
        }

        // Fallback to your existing legacy loading code
        console.log("📂 Using legacy project loading...");
        setLoadingMessage("Using legacy project loader...");

        let projectData;
        let project;

        if (
          window.electronAPI &&
          typeof window.electronAPI.readFile === "function"
        ) {
          projectData = window.electronAPI.readFile(filePath, "utf8");
          project = JSON.parse(projectData);
        } else {
          throw new Error("electronAPI readFile method not available");
        }

        // Your existing legacy loading logic here...
        // (Copy your existing video loading code from the working version)
        let videoSource = null;
        let videoFound = false;

        // Priority 1: Check for embedded video (new format)
        if (project.video?.embedded) {
          try {
            const binaryString = atob(project.video.embedded.data);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }

            const blob = new Blob([bytes], {
              type: project.video.embedded.mimeType,
            });
            const videoUrl = URL.createObjectURL(blob);

            videoSource = videoUrl;
            videoFound = true;
          } catch (error) {
            console.error("❌ Failed to restore embedded video:", error);
          }
        }
        // Priority 2: Check for video property (string filename)
        else if (project.video && typeof project.video === "string") {
          let projectDir;
          if (filePath.includes("\\")) {
            projectDir = filePath.substring(0, filePath.lastIndexOf("\\"));
          } else {
            projectDir = filePath.substring(0, filePath.lastIndexOf("/"));
          }

          const videoPath = `${projectDir}/${project.video}`;
          console.log("Constructed video path from project:", videoPath);

          if (window.electronAPI && window.electronAPI.fileExists) {
            const videoExists = window.electronAPI.fileExists(videoPath);
            if (videoExists) {
              videoSource = videoPath;
              videoFound = true;
              console.log("Video file found at:", videoPath);
            } else {
              console.warn("Video file not found at:", videoPath);
            }
          }
        }
        // Priority 3: Check for video file in project folder (nested video object)
        else if (project.video?.video) {
          let projectDir;
          if (filePath.includes("\\")) {
            projectDir = filePath.substring(0, filePath.lastIndexOf("\\"));
          } else {
            projectDir = filePath.substring(0, filePath.lastIndexOf("/"));
          }

          const videoPath = `${projectDir}/${project.video.video}`;

          if (window.electronAPI && window.electronAPI.fileExists) {
            const videoExists = window.electronAPI.fileExists(videoPath);
            if (videoExists) {
              videoSource = videoPath;
              videoFound = true;
            }
          }
        }

        // Set video source if found
        if (videoFound && videoSource) {
          if (
            typeof videoSource === "string" &&
            (videoSource.startsWith("/") || videoSource.includes(":\\"))
          ) {
            try {
              const buffer = window.electronAPI.readFileBuffer(videoSource);
              const uint8Array = new Uint8Array(buffer);

              const ext = videoSource.toLowerCase().split(".").pop();
              const mimeTypeMap = {
                mp4: "video/mp4",
                mov: "video/quicktime",
                avi: "video/x-msvideo",
                mkv: "video/x-matroska",
                webm: "video/webm",
              };
              const mimeType = mimeTypeMap[ext] || "video/mp4";

              const blob = new Blob([uint8Array], { type: mimeType });
              const fileName = videoSource.split(/[\\/]/).pop();
              const file = new File([blob], fileName, { type: mimeType });

              videoPlayerProps.setVideoSource(file);
              setVideoPathProps(file);
            } catch (error) {
              const fileUrl = videoSource.startsWith("file://")
                ? videoSource
                : `file://${videoSource}`;

              try {
                videoPlayerProps.setVideoSource(fileUrl);
                setVideoPathProps({ path: videoSource, name: project.video });
              } catch (urlError) {
                alert(`Failed to load video from: ${videoSource}`);
              }
            }
          } else {
            videoPlayerProps.setVideoSource(videoSource);
          }
        }

        // Load segments if they exist in the project (legacy format)
        if (project.segments && Array.isArray(project.segments)) {
          setSegments(project.segments);
          if (project.segments.length > 0) {
            setSelectedSegment(project.segments[0]);
          }
        }

        console.log("✅ Legacy project loading completed");
      } catch (error) {
        console.error("❌ Failed to open project:", error);
        alert(`Failed to open project file: ${error.message}`);
      } finally {
        setIsLoadingProject(false);
        setLoadingMessage("");
      }
    };

    // FIXED: Use callback pattern instead of property assignment
    if (
      window.electronAPI &&
      typeof window.electronAPI.onMenuOpenProject === "function"
    ) {
      // Use the callback pattern that already works in your preload
      const listener = window.electronAPI.onMenuOpenProject(handleOpenProject);
      console.log("✅ Menu handler set using callback pattern");

      return () => {
        // Cleanup
        if (window.electronAPI && window.electronAPI.removeAllListeners) {
          window.electronAPI.removeAllListeners("menu-open-project");
          console.log("🧹 Menu listeners cleaned up");
        }
      };
    } else {
      console.warn(
        "⚠️ electronAPI.onMenuOpenProject not available as function"
      );
    }

    const handleSaveProject = async () => {
      try {
        console.log("💾 Save project triggered from menu");

        if (!videoPlayerProps.videoSrc) {
          alert("No video loaded to save");
          return;
        }

        let videoPath = "";
        let videoBuffer = null;

        // Debug: Log what we're working with
        console.log("🔍 Debug video sources:", {
          videoSrc: typeof videoPlayerProps.videoSrc,
          videoFile: videoPlayerProps.videoFile
            ? videoPlayerProps.videoFile.constructor.name
            : "null",
          videoPathProps: videoPathProps,
          hasVideoFile: !!videoPlayerProps.videoFile,
        });

        // Method 1: Try to read from file path first (most reliable)
        if (videoPathProps && videoPathProps.path) {
          console.log(
            "📂 Method 1: Reading from file path:",
            videoPathProps.path
          );
          try {
            videoBuffer = await window.electronAPI.readFileAsBuffer(
              videoPathProps.path
            );
            videoPath = videoPathProps.path;
            console.log("✅ Successfully read from file path");
          } catch (error) {
            console.log("❌ Method 1 failed:", error.message);
          }
        }

        // Method 2: If no path, try File object
        if (!videoBuffer && videoPlayerProps.videoFile instanceof File) {
          console.log("📂 Method 2: Converting File object");
          try {
            const arrayBuffer = await videoPlayerProps.videoFile.arrayBuffer();
            videoBuffer = new Uint8Array(arrayBuffer);
            videoPath = videoPlayerProps.videoFile.name || "video.mp4";
            console.log("✅ Successfully converted File object");
          } catch (error) {
            console.log("❌ Method 2 failed:", error.message);
          }
        }

        // Method 3: Try string path as fallback
        if (
          !videoBuffer &&
          typeof videoPathProps === "string" &&
          videoPathProps
        ) {
          console.log("📂 Method 3: Reading from string path:", videoPathProps);
          try {
            videoBuffer = await window.electronAPI.readFileAsBuffer(
              videoPathProps
            );
            videoPath = videoPathProps;
            console.log("✅ Successfully read from string path");
          } catch (error) {
            console.log("❌ Method 3 failed:", error.message);
          }
        }

        // Validation
        if (!videoBuffer) {
          throw new Error(
            "Could not obtain video data for saving. Please reload the video and try again."
          );
        }

        // Ensure buffer is the right type
        if (
          !(videoBuffer instanceof Uint8Array) &&
          !(videoBuffer instanceof ArrayBuffer)
        ) {
          console.log("🔄 Converting buffer type");
          videoBuffer = new Uint8Array(videoBuffer);
        }

        console.log(`📊 Video buffer ready: ${videoBuffer.length} bytes`);

        // Generate hash
        const hashSlice = videoBuffer.slice(
          0,
          Math.min(1024, videoBuffer.length)
        );
        const videoHash = btoa(
          String.fromCharCode(...new Uint8Array(hashSlice))
        );

        // Prepare segments
        const segmentsToSave = {};
        segments.forEach((segment, index) => {
          const segmentKey = `segment${segment.id || index}`;
          segmentsToSave[segmentKey] = {
            segmentId: segment.id || index,
            segmentName: segment.name || `Segment ${index + 1}`,
            startTime: segment.start || 0,
            endTime: segment.end || 0,
            audioFile: segment.audioFile || null,
            characterSize: segment.originalSettings?.characterSize || "",
            weather: segment.originalSettings?.weather || "",
            environment: segment.originalSettings?.environment || "",
            groundTexture: segment.originalSettings?.groundTexture || "",
            groundMaterial: segment.originalSettings?.groundMaterial || "",
            footwear: segment.originalSettings?.footwear || "",
            fullPrompt: segment.originalSettings?.fullPrompt || "",
            settings: segment.originalSettings?.settings || {},
          };
        });

        console.log(
          `💾 Saving with ${Object.keys(segmentsToSave).length} segments`
        );

        // Call the save function with proper parameters
        const result = await window.electronAPI.saveVideo(
          videoPath, // string: video file name/path
          videoBuffer, // Uint8Array: video data
          videoHash, // string: hash for change detection
          segmentsToSave // object: segments data
        );

        if (result && result.success) {
          console.log("✅ Save successful:", result);

          // Update title
          const projectName = result.path
            ? result.path.split(/[\\/]/).pop()
            : "Saved Project";
          await window.electronAPI.updateWindowTitle(projectName);

          // Update project path
          setProjectFilePath(result.path);
        } else {
          throw new Error(result?.error || "Save failed for unknown reason");
        }
      } catch (error) {
        console.error("❌ Save failed:", error);
        alert(`Save failed: ${error.message}`);
      }
    };

    const handleSaveAsProject = async () => {
      try {
        console.log("💾 Save As project triggered");

        if (!videoPlayerProps.videoSrc) {
          alert("No video loaded to save");
          return;
        }

        let videoBuffer = null;
        let videoPath = "";

        // Use same buffer logic as regular save
        if (videoPathProps && videoPathProps.path) {
          videoBuffer = await window.electronAPI.readFileAsBuffer(
            videoPathProps.path
          );
          videoPath = videoPathProps.path;
        } else if (videoPlayerProps.videoFile instanceof File) {
          const arrayBuffer = await videoPlayerProps.videoFile.arrayBuffer();
          videoBuffer = new Uint8Array(arrayBuffer);
          videoPath = videoPlayerProps.videoFile.name || "video.mp4";
        } else if (typeof videoPathProps === "string" && videoPathProps) {
          videoBuffer = await window.electronAPI.readFileAsBuffer(
            videoPathProps
          );
          videoPath = videoPathProps;
        } else {
          throw new Error("No valid video source found");
        }

        // Ensure proper buffer type
        if (!(videoBuffer instanceof Uint8Array)) {
          videoBuffer = new Uint8Array(videoBuffer);
        }

        console.log("💾 Calling Save As with buffer size:", videoBuffer.length);

        const result = await window.electronAPI.saveProjectAs(
          videoPath,
          videoBuffer
        );

        if (result && result.success) {
          console.log("✅ Save As successful:", result);

          // Extract project name from saved path
          const projectName = result.path
            ? result.path.split(/[\\/]/).pop()
            : "Saved Project";

          // Update title immediately
          await window.electronAPI.updateWindowTitle(projectName);

          // FIXED: Use the path API from preload
          const plankFilePath = window.electronAPI.path.join(
            result.path,
            `${projectName}.plank`
          );
          setProjectFilePath(plankFilePath);

          console.log(
            "✅ Save As completed and title updated to:",
            projectName
          );
        } else {
          throw new Error(result?.error || "Save As failed");
        }
      } catch (error) {
        console.error("❌ Save As failed:", error);
        alert(`Save As failed: ${error.message}`);
      }
    };

    const handleClearProject = async () => {
      try {
        console.log("🆕 New project triggered from menu");

        // Reset all video and project states
        videoPlayerProps.setVideoSource(null);
        setVideoPathProps("");
        setProjectFilePath(null);
        setSelectedSegment(null);
        setSegments([]);
        setSegmentSettingsCache({});

        // Call the main process to reset tracking variables
        await window.electronAPI.clearProject();

        // Update title to "Untitled"
        await window.electronAPI.updateWindowTitle(null);

        console.log("✅ New project created - all states reset");
        console.log("📝 Next save will show file dialog");
      } catch (error) {
        console.error("❌ Clear project failed:", error);
      }
    };

    // Set up menu event listeners
    if (window.electronAPI) {
      // Open project handler (existing)
      const openListener =
        window.electronAPI.onMenuOpenProject(handleOpenProject);

      // Save project handlers
      const saveListener = window.electronAPI.onMenuSave(handleSaveProject);
      const saveAsListener =
        window.electronAPI.onMenuSaveAs(handleSaveAsProject);

      // Clear project handler
      const clearListener =
        window.electronAPI.onClearProject(handleClearProject);

      console.log("✅ All menu handlers set up with title management");

      return () => {
        // Cleanup all listeners
        if (window.electronAPI.removeAllListeners) {
          window.electronAPI.removeAllListeners("menu-open-project");
          window.electronAPI.removeAllListeners("save-project");
          window.electronAPI.removeAllListeners("save-project-as");
          window.electronAPI.removeAllListeners("clear-project");
          console.log("🧹 All menu listeners cleaned up");
        }
      };
    }
  }, [videoPlayerProps, segments, videoPathProps]);
  useEffect(() => {
    // Set initial title to "Untitled" when component first loads
    if (window.electronAPI && window.electronAPI.updateWindowTitle) {
      window.electronAPI.updateWindowTitle(null);
      console.log("🏷️ Initial title set to Untitled");
    }
  }, []);
  return (
    <>
      <ImportMediaModal />
      <ExportModal />
      <ExportMarkersModal />

      {/* Loading overlay for project opening */}
      {isLoadingProject && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999]">
          <div className="bg-neutral-800 rounded-lg p-8 max-w-md w-full mx-4">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              <div className="text-white text-center">
                <h3 className="text-lg font-semibold mb-2">Loading Project</h3>
                <p className="text-gray-300">{loadingMessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col items-center justify-between gap-5 w-full min-h-screen">
        <div
          className={`flex justify-center ${
            videoPlayerProps.videoSrc && page === "home"
              ? "items-end"
              : "items-center"
          } grow`}
        >
          <div className="flex lg:gap-6 xl:gap-10 items-start flex-row shrink-0 w-full justify-center">
            {page == "help" ? (
              <Help />
            ) : (
              <VideoContainer
                videoSrc={videoPlayerProps.videoSrc}
                videoPlayerProps={videoPlayerProps}
                videoPathProps={videoPlayerProps.videoFile}
                dragDropProps={dragDropProps}
                onVideoFile={handleVideoFile}
                selectedSegment={selectedSegment}
                segments={segments}
              />
            )}
            {videoPlayerProps.videoSrc && (
              <VideoSetting
                videoSrc={videoPlayerProps.videoSrc}
                videoPathProps={videoPlayerProps.videoFile}
                onSegmentUpdate={handleSegmentUpdate}
                projectFilePath={projectFilePath}
                originalVideoPath={videoPathProps?.path || videoPathProps?.name}
                videoFile={videoPathProps}
                setSegments={setSegments}
                currentTime={videoPlayerProps.currentTime}
                duration={videoPlayerProps.duration}
                isPlaying={videoPlayerProps.isPlaying}
                onSeek={videoPlayerProps.handleSeek}
                onPlayPause={videoPlayerProps.togglePlay}
                volume={videoPlayerProps.volume}
                onVolumeChange={videoPlayerProps.handleVolumeChange}
                selectedSegment={selectedSegment}
                onSegmentSelect={handleSegmentSelect}
                onSegmentsChange={handleSegmentsChange}
                segments={segments}
                // FIXED: Pass the cached settings for the selected segment
                segmentSettingsCache={segmentSettingsCache}
              />
            )}
          </div>
        </div>

        {videoPlayerProps.videoSrc && page == "home" && (
          <div className="timeline-wrapper w-full mt-4">
            <VideoTimeline
              videoSrc={videoPlayerProps.videoSrc}
              originalVideoPath={videoPathProps?.path || videoPathProps?.name}
              videoFile={videoPathProps}
              projectFilePath={projectFilePath}
              currentTime={videoPlayerProps.currentTime}
              duration={videoPlayerProps.duration}
              isPlaying={videoPlayerProps.isPlaying}
              onSeek={videoPlayerProps.handleSeek}
              onPlayPause={videoPlayerProps.togglePlay}
              volume={videoPlayerProps.volume}
              onVolumeChange={videoPlayerProps.handleVolumeChange}
              selectedSegment={selectedSegment}
              onSegmentSelect={handleSegmentSelect}
              onSegmentsChange={handleSegmentsChange}
              segments={segments}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default VideoPlayerWithWaveform;
