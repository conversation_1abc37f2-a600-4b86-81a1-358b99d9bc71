{"name": "mochachai-fe-react", "private": true, "version": "0.0.0", "type": "module", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "vite build", "build-production": "npm run build && npm run setup-python && npm run setup-mmaudio && electron-builder", "build-electron": "electron-builder", "setup-python": "npm run copy-python && npm run install-python-deps", "copy-python": "shx cp -r electron/python-scripts dist/", "install-python-deps": "cd dist/python-scripts && python3 -m venv .venv", "lint": "eslint .", "preview": "vite preview", "electron": "npm run setup-mmaudio && concurrently -k \"vite\" \"wait-on http://localhost:5173 && electron .\"", "dist": "vite build && electron-builder", "start": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && npm run electron\"", "setup-mmaudio": "npm run clone-mmaudio && npm run download-mmaudio-weights", "clone-mmaudio": "node electron/clone-mmaudio.js", "download-mmaudio-weights": "node electron/download-mmaudio-weights.js", "clean-mmaudio": "shx rm -rf mmaudio"}, "dependencies": {"@electron/remote": "^2.1.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "@tailwindcss/postcss": "^4.1.7", "buffer": "^6.0.3", "child_process": "^1.0.2", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^36.3.2", "electron-builder": "^26.0.12", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "shx": "^0.4.0", "vite": "^6.3.5", "wait-on": "^8.0.3"}, "build": {"appId": "com.mochachai.app", "productName": "ＰＬＡＮＣＫ", "mac": {"target": ["dmg", "zip"], "category": "public.app-category.utilities", "icon": "assets/icon.icns"}, "win": {"target": "nsis"}, "files": ["build/**/*", "main/**/*", "dist/**/*", "electron/**/*"], "directories": {"buildResources": "assets", "output": "dist"}, "extraResources": [{"from": "dist/python-scripts", "to": "python-scripts"}, {"from": "electron/python-scripts/weights", "to": "weights", "filter": ["**/*"]}, {"from": "electron/python-scripts/ext_weights", "to": "ext_weights", "filter": ["**/*"]}]}}