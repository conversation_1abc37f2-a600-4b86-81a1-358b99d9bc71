/* eslint-disable no-unused-vars */
/**
 * Python stdin Manager - Complete stdin/stdout Integration
 * Simplified manager for stdin communication only
 */

import { ipcMain } from "electron";
import { spawn, exec } from "child_process";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class PythonStdinManager {
  constructor(options = {}) {
    this.config = {
      serverScript: options.serverScript || "sound_post_server.py",
      timeout: options.timeout || 30000,
      autoInstallDeps: options.autoInstallDeps !== false,
      ...options
    };

    this.pythonProcess = null;
    this.isReady = false;
    this.startupPromise = null;
    this.pendingRequests = new Map();
    this.requestCounter = 0;
    
    // Auto-register IPC handlers
    this.registerIPCHandlers();
    
    console.log("🐍 Python stdin Manager initialized");
  }

  /**
   * Register all IPC handlers
   */
  registerIPCHandlers() {
    ipcMain.handle("python:run-analysis", async (event, payload) => {
      return await this.analyze(payload);
    });

    ipcMain.handle("python:check-server", async () => {
      return await this.checkHealth();
    });

    ipcMain.handle("python:start-server", async () => {
      return await this.start();
    });

    ipcMain.handle("python:stop-server", async () => {
      this.stop();
      return { success: true };
    });

    ipcMain.handle("python:restart-server", async () => {
      return await this.restart();
    });

    ipcMain.handle("python:server-info", async () => {
      return this.getInfo();
    });

    console.log("✅ Python stdin IPC handlers registered");
  }

  /**
   * Check Python installation
   */
  async checkPythonInstallation() {
    return new Promise((resolve) => {
      exec('python --version', (error, stdout, stderr) => {
        if (error) {
          exec('python3 --version', (error3, stdout3, stderr3) => {
            resolve(error3 ? 
              { installed: false, command: null } : 
              { installed: true, command: 'python3', version: stdout3.trim() }
            );
          });
        } else {
          resolve({ installed: true, command: 'python', version: stdout.trim() });
        }
      });
    });
  }

  /**
   * Install Python dependencies
   */
  async installDependencies() {
    if (!this.config.autoInstallDeps) {
      return { success: true, skipped: true };
    }

    return new Promise((resolve) => {
      // Check for requirements.txt in python-script folder first, then root
      const requirementsPaths = [
        path.join(__dirname, "python-script", "requirements.txt"),
        path.join(__dirname, "requirements.txt")
      ];
      
      let requirementsPath = null;
      for (const reqPath of requirementsPaths) {
        if (fs.existsSync(reqPath)) {
          requirementsPath = reqPath;
          break;
        }
      }
      
      if (!requirementsPath) {
        console.log("⚠️ requirements.txt not found, skipping dependency installation");
        resolve({ success: true, skipped: true });
        return;
      }

      console.log(`📦 Installing Python dependencies from: ${requirementsPath}`);
      
      const pythonCheck = this.checkPythonInstallation();
      if (!pythonCheck.installed) {
        resolve({ success: false, error: "Python not installed" });
        return;
      }

      const pipCommand = pythonCheck.command === 'python3' ? 'pip3' : 'pip';
      
      exec(`${pipCommand} install -r "${requirementsPath}"`, (error, stdout, stderr) => {
        if (error) {
          console.error("❌ Failed to install dependencies:", error);
          resolve({ 
            success: false, 
            error: error.message,
            suggestion: `Please run manually: pip install -r "${requirementsPath}"`
          });
        } else {
          console.log("✅ Python dependencies installed successfully");
          resolve({ success: true, output: stdout });
        }
      });
    });
  }

  /**
   * Start the Python process
   */
  async start() {
    if (this.startupPromise) {
      return this.startupPromise;
    }

    this.startupPromise = new Promise( (resolve) => {
      try {
        // Check if already running
        if (this.pythonProcess && this.isReady) {
          resolve({ success: true, alreadyRunning: true });
          return;
        }

        // Check Python installation
        const pythonCheck =  this.checkPythonInstallation();
        if (!pythonCheck.installed) {
          resolve({
            success: false,
            error: "Python not installed or not in PATH"
          });
          return;
        }

        console.log(`🐍 Using Python: ${pythonCheck.command} (${pythonCheck.version})`);

        // Install dependencies
        const depInstall =  this.installDependencies();
        if (!depInstall.success && !depInstall.skipped) {
          resolve({
            success: false,
            error: "Failed to install Python dependencies",
            details: depInstall.error,
            suggestion: depInstall.suggestion
          });
          return;
        }

        // Check script exists
        const serverPath = path.join(__dirname, this.config.serverScript);
        if (!fs.existsSync(serverPath)) {
          resolve({
            success: false,
            error: `${this.config.serverScript} not found at: ${serverPath}`
          });
          return;
        }

        console.log("🚀 Starting Python AI server (stdin/stdout mode)...");
        
        // Spawn Python process
        this.pythonProcess = spawn(pythonCheck.command, [serverPath], {
          stdio: ['pipe', 'pipe', 'pipe'],
          cwd: this.config.workingDirectory || __dirname  // Use custom working directory if provided
        });

        this.setupProcessHandlers(resolve);

      } catch (error) {
        console.error("❌ Error starting Python process:", error);
        resolve({ success: false, error: error.message });
      }
    });

    return this.startupPromise;
  }

  /**
   * Setup process event handlers
   */
  setupProcessHandlers(resolve) {
    let outputBuffer = "";
    let resolved = false;

    // Handle stdout (responses from Python)
    this.pythonProcess.stdout.on('data', (data) => {
      outputBuffer += data.toString();
      
      // Process complete JSON messages (one per line)
      const lines = outputBuffer.split('\n');
      outputBuffer = lines.pop(); // Keep incomplete line in buffer
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handlePythonResponse(response, resolve, () => { resolved = true; });
          } catch (e) {
            console.error("❌ Failed to parse Python response:", line);
          }
        }
      });
    });

    // Handle stderr (logs from Python)
    this.pythonProcess.stderr.on('data', (data) => {
      const logMessage = data.toString().trim();
      console.log(`🐍 Python: ${logMessage}`);
    });

    // Handle process close
    this.pythonProcess.on('close', (code) => {
      console.log(`🐍 Python process closed with code: ${code}`);
      this.cleanup();
      
      if (!resolved) {
        resolve({
          success: false,
          error: `Python process exited with code ${code}`
        });
      }
    });

    // Handle process errors
    this.pythonProcess.on('error', (error) => {
      console.error('🐍 Python process error:', error);
      this.cleanup();
      
      if (!resolved) {
        resolve({
          success: false,
          error: `Python process failed: ${error.message}`
        });
      }
    });

    // Timeout for startup
    setTimeout(() => {
      if (!resolved && !this.isReady) {
        this.cleanup();
        resolve({
          success: false,
          error: "Python server failed to start within 10 seconds"
        });
        resolved = true;
      }
    }, 10000);
  }

  /**
   * Handle responses from Python
   */
  handlePythonResponse(response, startupResolve = null, markResolved = null) {
    console.log(`📨 Python response: ${response.status}`);
    
    // Handle startup ready signal
    if (response.status === 'ready' && startupResolve && !this.isReady) {
      this.isReady = true;
      console.log("✅ Python AI server is ready!");
      startupResolve({ success: true });
      if (markResolved) markResolved();
      return;
    }

    // Handle analysis responses
    if (response.request_id || response.analysis_id) {
      const requestId = response.request_id || response.analysis_id;
      const pendingResolve = this.pendingRequests.get(requestId);
      
      if (pendingResolve) {
        this.pendingRequests.delete(requestId);
        pendingResolve({
          success: response.status === 'success',
          data: response.results,
          analysis_id: response.analysis_id,
          processing_time: response.processing_time,
          error: response.status === 'error' ? response.message : null
        });
      }
    }
  }

  /**
   * Send command to Python process
   */
  async sendCommand(data) {
    return new Promise((resolve) => {
      if (!this.pythonProcess || !this.isReady) {
        resolve({
          success: false,
          error: "Python process is not ready"
        });
        return;
      }

      try {
        // Generate unique request ID
        const requestId = `req_${++this.requestCounter}_${Date.now()}`;
        const requestData = { ...data, request_id: requestId };

        // Store resolve function
        this.pendingRequests.set(requestId, resolve);

        // Send to Python
        const jsonData = JSON.stringify(requestData) + '\n';
        this.pythonProcess.stdin.write(jsonData);

        // Set timeout
        setTimeout(() => {
          if (this.pendingRequests.has(requestId)) {
            this.pendingRequests.delete(requestId);
            resolve({
              success: false,
              error: "Request timed out"
            });
          }
        }, this.config.timeout);

      } catch (error) {
        resolve({
          success: false,
          error: `Failed to send command: ${error.message}`
        });
      }
    });
  }

  /**
   * Run analysis
   */
  async analyze(payload) {
    try {
      // Ensure server is started
      if (!this.pythonProcess || !this.isReady) {
        const startResult = await this.start();
        if (!startResult.success) {
          return {
            success: false,
            error: "Failed to start Python server",
            details: startResult.error
          };
        }
      }

      console.log("🔄 Sending analysis request to Python...");
      
      const result = await this.sendCommand(payload);
      
      if (result.success) {
        console.log("✅ Analysis completed successfully");
      }
      
      return result;

    } catch (error) {
      console.error("❌ Python analysis failed:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Check if server is running
   */
  async checkHealth() {
    if (!this.pythonProcess || !this.isReady) {
      return { running: false };
    }
    
    try {
      const response = await this.sendCommand({ command: "STATUS" });
      return { running: true, status: response };
    } catch (error) {
      return { running: false, error: error.message };
    }
  }

  /**
   * Stop the Python process
   */
  stop() {
    if (this.pythonProcess) {
      console.log("🛑 Stopping Python server...");
      
      try {
        // Send quit command gracefully
        this.pythonProcess.stdin.write("QUIT\n");
        
        // Give it time to shut down gracefully
        setTimeout(() => {
          if (this.pythonProcess) {
            this.pythonProcess.kill('SIGTERM');
          }
        }, 3000);
      } catch (e) {
        this.pythonProcess.kill('SIGTERM');
      }
    }
    
    this.cleanup();
  }

  /**
   * Cleanup internal state
   */
  cleanup() {
    this.pythonProcess = null;
    this.isReady = false;
    this.startupPromise = null;
    
    // Reject all pending requests
    this.pendingRequests.forEach((resolve, requestId) => {
      resolve({
        success: false,
        error: "Python process terminated"
      });
    });
    this.pendingRequests.clear();
  }

  /**
   * Get server info
   */
  getInfo() {
    return {
      running: !!this.pythonProcess,
      ready: this.isReady,
      script: this.config.serverScript,
      pendingRequests: this.pendingRequests.size,
      method: "stdin"
    };
  }

  /**
   * Restart server
   */
  async restart() {
    console.log("🔄 Restarting Python server...");
    this.stop();
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await this.start();
  }
}

export default PythonStdinManager;