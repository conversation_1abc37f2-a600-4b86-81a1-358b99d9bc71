/* eslint-disable react-hooks/exhaustive-deps */
import {
  Box,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Slider,
  TextField,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import SelectEnv from "./environtment-criteria/SelectEnv";
import Button from "./Button";
import ItemSetting from "./ItemSetting";
import PopupSetting from "./PopupSetting";
import { CheckedIcon, UncheckedIcon } from "./icon/CutomIcon";
import {
  AudioTypeItems,
  envItem,
  FootwearItems,
  GroundMaterialItems,
  GroundTextureItems,
  VideoTypeItems,
  weatherItems,
} from "../data/VideoSetting";
import { useAppContext } from "../hooks/useAppContext";
import { useSaveProject } from "../hooks/useSaveProject";
import { useProjectPath } from "../hooks/useProjectPath";

const VideoSetting = ({
  videoSrc,
  videoPathProps,
  selectedSegment,
  onSegmentUpdate,
  segments = [],
  onSegmentsChange,
  segmentSettingsCache = {},
}) => {
  // Define initial default values as constants
  const INITIAL_DEFAULTS = {
    weather: "dry",
    environtment: "indoor",
    groundTexture: "soft",
    groundMaterial: "carpet",
    videoType: "mp4",
    audioType: "wav",
    footwear: "barefoot",
    seed: 0,
    qualitySounds: 50,
    guidenceStrength: 7,
    characterSize: "big",
    fullPrompt: "",
    negativePrompt:
      "talking, speech, voice, dialogue, conversation, ambient noise, background noise, room tone, breathing, fabric rustle, music, chatter, murmuring, whispering, echo",
  };

  const [weather, setWeather] = useState(INITIAL_DEFAULTS.weather);
  const [environtment, setEnvirontment] = useState(
    INITIAL_DEFAULTS.environtment
  );
  const [groundTexture, setGroundTexture] = useState(
    INITIAL_DEFAULTS.groundTexture
  );
  const [groundMaterial, setGroundMaterial] = useState(
    INITIAL_DEFAULTS.groundMaterial
  );
  const [videoType, setVideoType] = useState(INITIAL_DEFAULTS.videoType);
  const [audioType, setAudioType] = useState(INITIAL_DEFAULTS.audioType);
  const [footwear, setFootwear] = useState(INITIAL_DEFAULTS.footwear);
  const [showPopup, setShowPopup] = useState("");
  const [seed, setSeed] = useState(INITIAL_DEFAULTS.seed);
  const [qualitySounds, setQualitySounds] = useState(
    INITIAL_DEFAULTS.qualitySounds
  );
  const [guidenceStrength, setGuidenceStrength] = useState(
    INITIAL_DEFAULTS.guidenceStrength
  );
  const { setPage } = useAppContext();
  const [isLoading, setIsLoding] = useState(false);
  const [characterSize, setCharacterSize] = useState(
    INITIAL_DEFAULTS.characterSize
  );
  const [fullPrompt, setFullPrompt] = useState(INITIAL_DEFAULTS.fullPrompt);
  const [negativePrompt, setNegativePrompt] = useState(
    INITIAL_DEFAULTS.negativePrompt
  );
  const [segmentAudioFiles, setSegmentAudioFiles] = useState({});
  const [segmentSettings, setSegmentSettings] = useState({});

  const { saveProject, savedPath } = useSaveProject(videoSrc, videoPathProps);
  const { projectUrl, pathRef, updatePath } = useProjectPath();

  // Helper function to reset all states to initial defaults
  const resetToDefaults = () => {
    console.log("🔄 Resetting all states to initial default values");
    setWeather(INITIAL_DEFAULTS.weather);
    setEnvirontment(INITIAL_DEFAULTS.environtment);
    setGroundTexture(INITIAL_DEFAULTS.groundTexture);
    setGroundMaterial(INITIAL_DEFAULTS.groundMaterial);
    setVideoType(INITIAL_DEFAULTS.videoType);
    setAudioType(INITIAL_DEFAULTS.audioType);
    setFootwear(INITIAL_DEFAULTS.footwear);
    setSeed(INITIAL_DEFAULTS.seed);
    setQualitySounds(INITIAL_DEFAULTS.qualitySounds);
    setGuidenceStrength(INITIAL_DEFAULTS.guidenceStrength);
    setCharacterSize(INITIAL_DEFAULTS.characterSize);
    setFullPrompt(INITIAL_DEFAULTS.fullPrompt);
    setNegativePrompt(INITIAL_DEFAULTS.negativePrompt);
  };

  const savePlankFile = async (segmentId, settings, videoFileName = null) => {
    if (!selectedSegment || !projectUrl) {
      console.log(
        "Cannot save .plank file: no segment selected or project path"
      );
      return;
    }

    try {
      // Use savedVideoFileName if provided, otherwise fall back to segment name
      const baseFileName = videoFileName || selectedSegment.name;
      const plankFilePath = `${projectUrl}/${baseFileName}.plank`;

      const plankData = {
        segmentId: segmentId,
        segmentName: selectedSegment.name,
        videoFileName: videoFileName || null,
        characterSize: settings.characterSize,
        weather: settings.weather,
        environment: settings.environment,
        groundTexture: settings.groundTexture,
        groundMaterial: settings.groundMaterial,
        footwear: settings.footwear,
        fullPrompt: settings.fullPrompt,
        negativePrompt: settings.negativePrompt,
        seed: settings.seed,
        qualitySounds: settings.qualitySounds,
        guidenceStrength: settings.guidenceStrength,
        lastModified: new Date().toISOString(),
      };

      // Save the .plank file
      if (
        window.electronAPI &&
        typeof window.electronAPI.writeFile === "function"
      ) {
        await window.electronAPI.writeFile(
          plankFilePath,
          JSON.stringify(plankData, null, 2)
        );
        console.log(
          `✅ Saved .plank file: ${baseFileName}.plank for segment: ${selectedSegment.name}`
        );
      } else {
        console.warn("Cannot save .plank file: Electron API not available");
      }
    } catch (error) {
      console.error(
        `Error saving .plank file for segment ${selectedSegment.name}:`,
        error
      );
    }
  };

  const saveCurrentSettingsForSegment = (segmentId, videoFileName = null) => {
    const currentSettings = {
      characterSize: characterSize,
      weather: weather,
      environment: environtment,
      groundTexture: groundTexture,
      groundMaterial: groundMaterial,
      footwear: footwear,
      fullPrompt: fullPrompt,
      negativePrompt: negativePrompt,
      seed: seed,
      qualitySounds: qualitySounds,
      guidenceStrength: guidenceStrength,
    };

    setSegmentSettings((prev) => ({
      ...prev,
      [segmentId]: currentSettings,
    }));

    // Also save to .plank file with videoFileName
    savePlankFile(segmentId, currentSettings, videoFileName);

    return currentSettings;
  };

  useEffect(() => {
    const loadSegmentSettings = async () => {
      if (!selectedSegment) {
        console.log("No segment selected");
        // Reset to defaults when no segment is selected
        resetToDefaults();
        return;
      }

      console.log(
        `🔄 Loading settings for segment: ${selectedSegment.name} (ID: ${selectedSegment.id})`
      );

      // PRIORITY 1: Check if we have cached settings from project loading
      if (segmentSettingsCache && segmentSettingsCache[selectedSegment.id]) {
        console.log(
          "📋 Found cached settings from project loading:",
          segmentSettingsCache[selectedSegment.id]
        );
        const cachedSettings = segmentSettingsCache[selectedSegment.id];

        // Apply cached settings directly
        setCharacterSize(
          cachedSettings.characterSize || INITIAL_DEFAULTS.characterSize
        );
        setWeather(cachedSettings.weather || INITIAL_DEFAULTS.weather);
        setEnvirontment(
          cachedSettings.environment || INITIAL_DEFAULTS.environtment
        );
        setGroundTexture(
          cachedSettings.groundTexture || INITIAL_DEFAULTS.groundTexture
        );
        setGroundMaterial(
          cachedSettings.groundMaterial || INITIAL_DEFAULTS.groundMaterial
        );
        setFootwear(cachedSettings.footwear || INITIAL_DEFAULTS.footwear);
        setFullPrompt(cachedSettings.fullPrompt || INITIAL_DEFAULTS.fullPrompt);

        // Handle nested settings
        if (cachedSettings.settings) {
          setNegativePrompt(
            cachedSettings.settings.negativePrompt ||
              INITIAL_DEFAULTS.negativePrompt
          );
          setSeed(cachedSettings.settings.seed || INITIAL_DEFAULTS.seed);
          setQualitySounds(
            cachedSettings.settings.qualitySounds ||
              INITIAL_DEFAULTS.qualitySounds
          );
          setGuidenceStrength(
            cachedSettings.settings.guidenceStrength ||
              INITIAL_DEFAULTS.guidenceStrength
          );
        } else {
          setNegativePrompt(INITIAL_DEFAULTS.negativePrompt);
          setSeed(INITIAL_DEFAULTS.seed);
          setQualitySounds(INITIAL_DEFAULTS.qualitySounds);
          setGuidenceStrength(INITIAL_DEFAULTS.guidenceStrength);
        }

        console.log(
          `✅ Applied cached settings for segment: ${selectedSegment.name}`
        );
        return;
      }

      // PRIORITY 2: Check if we have runtime cached settings for this segment
      if (segmentSettings[selectedSegment.id]) {
        console.log(
          `📋 Found runtime cached settings for segment ${selectedSegment.name}`
        );
        const cachedSettings = segmentSettings[selectedSegment.id];

        // Apply cached settings
        setCharacterSize(cachedSettings.characterSize);
        setWeather(cachedSettings.weather);
        setEnvirontment(cachedSettings.environment);
        setGroundTexture(cachedSettings.groundTexture);
        setGroundMaterial(cachedSettings.groundMaterial);
        setFootwear(cachedSettings.footwear);
        setFullPrompt(cachedSettings.fullPrompt);
        setNegativePrompt(cachedSettings.negativePrompt);
        setSeed(cachedSettings.seed);
        setQualitySounds(cachedSettings.qualitySounds);
        setGuidenceStrength(cachedSettings.guidenceStrength);

        console.log(
          `✅ Applied runtime cached settings for segment: ${selectedSegment.name}`
        );
        console.log(`👟 Footwear set to: ${cachedSettings.footwear}`);
        return;
      }

      // PRIORITY 3: Try to load from .plank file (existing logic)
      if (!projectUrl) {
        console.log("No project URL available, resetting to defaults");
        resetToDefaults();
        return;
      }

      try {
        // Extract video filename from videoSrc or videoPathProps
        let videoFileName = null;

        // Try to get filename from videoSrc first
        if (videoSrc && typeof videoSrc === "string") {
          const srcParts = videoSrc.split(/[/\\]/);
          const fullName = srcParts[srcParts.length - 1];
          // Remove file extension to get base name
          videoFileName = fullName.replace(/\.[^/.]+$/, "");
        }

        // If no videoSrc, try videoPathProps
        if (
          !videoFileName &&
          videoPathProps &&
          typeof videoPathProps === "string"
        ) {
          const pathParts = videoPathProps.split(/[/\\]/);
          const fullName = pathParts[pathParts.length - 1];
          // Remove file extension to get base name
          videoFileName = fullName.replace(/\.[^/.]+$/, "");
        }

        // If still no filename, try savedPath
        if (!videoFileName && savedPath) {
          const pathParts = savedPath.split(/[/\\]/);
          const fullName = pathParts[pathParts.length - 1];
          // Remove file extension to get base name
          videoFileName = fullName.replace(/\.[^/.]+$/, "");
        }

        // Construct the .plank file path - use videoFileName if available, otherwise segment name
        let plankFilePath;
        if (videoFileName) {
          plankFilePath = `${projectUrl}/${videoFileName}.plank`;
        } else {
          plankFilePath = `${projectUrl}/${selectedSegment.name}.plank`;
        }

        console.log(`📁 Loading settings from: ${plankFilePath}`);

        // Create a mapping function to handle value normalization
        const normalizeValue = (value, validOptions = []) => {
          if (!value) return value;

          // Convert to lowercase for comparison
          const lowerValue = value.toLowerCase();

          // Find matching option (case-insensitive)
          const matchingOption = validOptions.find(
            (option) =>
              option.toLowerCase() === lowerValue ||
              option.toLowerCase().includes(lowerValue) ||
              lowerValue.includes(option.toLowerCase())
          );

          return matchingOption || value;
        };

        // Get valid options from your data arrays
        const validFootwearOptions = [
          "barefoot",
          "sandals",
          "hard sole shoes",
          "soft sole shoes",
          "sneakers",
        ];
        const validWeatherOptions = ["dry", "wet", "snow"];
        const validEnvironmentOptions = ["indoor", "outdoor"];
        const validGroundTextureOptions = ["soft", "hard", "rough", "smooth"];
        const validGroundMaterialOptions = [
          "carpet",
          "wood",
          "concrete",
          "grass",
          "gravel",
        ];

        // Check if we're in Electron environment and file exists
        if (
          window.electronAPI &&
          typeof window.electronAPI.fileExists === "function"
        ) {
          const fileExists = await window.electronAPI.fileExists(plankFilePath);

          if (!fileExists) {
            // If the primary file doesn't exist and we used videoFileName,
            // try falling back to segment name
            if (videoFileName) {
              const fallbackPath = `${projectUrl}/${selectedSegment.name}.plank`;
              console.log(
                `❌ Primary .plank file not found, trying fallback: ${fallbackPath}`
              );

              const fallbackExists = await window.electronAPI.fileExists(
                fallbackPath
              );
              if (fallbackExists) {
                plankFilePath = fallbackPath;
                console.log(`✅ Using fallback .plank file: ${fallbackPath}`);
              } else {
                console.log(
                  `❌ No .plank file found for segment: ${selectedSegment.name} - resetting to defaults`
                );
                // RESET TO DEFAULTS when no .plank file is found
                resetToDefaults();
                return;
              }
            } else {
              console.log(
                `❌ No .plank file found for segment: ${selectedSegment.name} - resetting to defaults`
              );
              // RESET TO DEFAULTS when no .plank file is found
              resetToDefaults();
              return;
            }
          }

          // Read the .plank file
          let plankData;
          if (typeof window.electronAPI.readFileBuffer === "function") {
            try {
              const buffer = await window.electronAPI.readFileBuffer(
                plankFilePath
              );
              const text = new TextDecoder().decode(buffer);
              plankData = JSON.parse(text);
            } catch (bufferError) {
              console.warn(
                "Failed to read as buffer, trying direct read:",
                bufferError
              );
              // Fallback to direct file reading if available
              if (typeof window.electronAPI.readFile === "function") {
                const text = await window.electronAPI.readFile(
                  plankFilePath,
                  "utf8"
                );
                plankData = JSON.parse(text);
              } else {
                throw new Error("No file reading method available");
              }
            }
          } else {
            // For web environment or if buffer method not available
            const response = await fetch(plankFilePath);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            plankData = await response.json();
          }

          console.log(
            `📄 Loaded .plank data for ${selectedSegment.name}:`,
            plankData
          );

          // FIRST: Reset to defaults, then apply loaded settings
          // This ensures any missing properties get default values
          resetToDefaults();

          // Update all state values based on the .plank file data with proper normalization
          if (plankData.characterSize !== undefined) {
            console.log(`📏 Setting characterSize: ${plankData.characterSize}`);
            setCharacterSize(plankData.characterSize);
          }
          if (plankData.weather !== undefined) {
            const normalizedWeather = normalizeValue(
              plankData.weather,
              validWeatherOptions
            );
            console.log(
              `🌤️ Setting weather: ${plankData.weather} -> ${normalizedWeather}`
            );
            setWeather(normalizedWeather);
          }
          if (plankData.environment !== undefined) {
            const normalizedEnvironment = normalizeValue(
              plankData.environment,
              validEnvironmentOptions
            );
            console.log(
              `🏠 Setting environment: ${plankData.environment} -> ${normalizedEnvironment}`
            );
            setEnvirontment(normalizedEnvironment);
          }
          if (plankData.groundTexture !== undefined) {
            const normalizedGroundTexture = normalizeValue(
              plankData.groundTexture,
              validGroundTextureOptions
            );
            console.log(
              `🌫️ Setting groundTexture: ${plankData.groundTexture} -> ${normalizedGroundTexture}`
            );
            setGroundTexture(normalizedGroundTexture);
          }
          if (plankData.groundMaterial !== undefined) {
            const normalizedGroundMaterial = normalizeValue(
              plankData.groundMaterial,
              validGroundMaterialOptions
            );
            console.log(
              `🪨 Setting groundMaterial: ${plankData.groundMaterial} -> ${normalizedGroundMaterial}`
            );
            setGroundMaterial(normalizedGroundMaterial);
          }
          if (plankData.footwear !== undefined) {
            const normalizedFootwear = normalizeValue(
              plankData.footwear,
              validFootwearOptions
            );
            console.log(
              `👟 Setting footwear: ${plankData.footwear} -> ${normalizedFootwear}`
            );
            setFootwear(normalizedFootwear);
          }
          if (plankData.fullPrompt !== undefined) {
            console.log(`📝 Setting fullPrompt: ${plankData.fullPrompt}`);
            setFullPrompt(plankData.fullPrompt);
          }
          if (plankData.negativePrompt !== undefined) {
            console.log(
              `❌ Setting negativePrompt: ${plankData.negativePrompt.substring(
                0,
                50
              )}...`
            );
            setNegativePrompt(plankData.negativePrompt);
          }
          if (plankData.seed !== undefined) {
            console.log(`🌱 Setting seed: ${plankData.seed}`);
            setSeed(plankData.seed);
          }
          if (plankData.qualitySounds !== undefined) {
            console.log(`🔊 Setting qualitySounds: ${plankData.qualitySounds}`);
            setQualitySounds(plankData.qualitySounds);
          }
          if (plankData.guidenceStrength !== undefined) {
            console.log(
              `💪 Setting guidenceStrength: ${plankData.guidenceStrength}`
            );
            setGuidenceStrength(plankData.guidenceStrength);
          }

          console.log(
            `✅ Successfully loaded settings from .plank file for segment: ${selectedSegment.name}`
          );
        } else {
          // For web environment without Electron API
          console.log(
            "🌐 Using web environment (no Electron API) - resetting to defaults"
          );
          resetToDefaults();
        }
      } catch (error) {
        console.error(
          `❌ Error loading .plank file for segment ${selectedSegment.name}:`,
          error
        );
        // RESET TO DEFAULTS when there's an error loading the .plank file
        console.log("🔄 Resetting to defaults due to error");
        resetToDefaults();

        // Don't show alert for missing files, just log the error
        if (!error.message.includes("HTTP error! status: 404")) {
          console.warn(`Failed to load settings: ${error.message}`);
        }
      }
    };

    // Load settings when segment changes
    loadSegmentSettings();
  }, [
    selectedSegment?.id,
    projectUrl,
    videoSrc,
    videoPathProps,
    savedPath,
    segmentSettingsCache,
  ]);
  useEffect(() => {
    updatePath(savedPath);
  }, [savedPath, updatePath]);

  // Add formatTime function that was missing
  const formatTime = (timeInSeconds) => {
    if (!timeInSeconds && timeInSeconds !== 0) return "00:00";
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  // 1. Synchronize paths using ref to always get fresh value
  const projectUrlRef = useRef(projectUrl);
  useEffect(() => {
    projectUrlRef.current = projectUrl;
  }, [projectUrl]);

  const handleReplaceSegmentAudio = async (audioFileName) => {
    if (!selectedSegment) {
      console.log("No segment selected for audio replacement");
      return;
    }

    // Use pathRef.current from the useProjectPath hook (already available in component)
    const projectPath = pathRef.current;

    // Check if we have a valid path
    if (!projectPath || typeof projectPath !== "string") {
      console.error(
        "No valid pathRef available for audio replacement:",
        projectPath
      );
      return;
    }

    console.log("Using pathRef from hook:", projectPath);

    const audioPath = `${projectPath}/ai/${audioFileName}`;

    console.log("Constructed audio path:", audioPath);

    try {
      console.log(`Replacing audio for segment: ${selectedSegment.name}`);
      console.log(
        `Segment time range: ${formatTime(
          selectedSegment.start
        )} to ${formatTime(selectedSegment.end)}`
      );
      console.log(`Audio file path: ${audioPath}`);

      // Create audio element to load the replacement audio
      let audio;

      if (
        window.electronAPI &&
        typeof window.electronAPI.readFileBuffer === "function"
      ) {
        try {
          // For Electron environment, read file as buffer first
          console.log("Attempting to read audio file as buffer...");
          const buffer = await window.electronAPI.readFileBuffer(audioPath);
          const blob = new Blob([buffer], { type: "audio/wav" });
          const audioUrl = URL.createObjectURL(blob);
          audio = new Audio(audioUrl);
          console.log("Successfully loaded audio from buffer");
        } catch (fileError) {
          console.warn(
            "Failed to read audio file as buffer, trying direct path:",
            fileError
          );
          // Fallback to direct file path
          const fileUrl = audioPath.startsWith("file://")
            ? audioPath
            : `file://${audioPath}`;
          audio = new Audio(fileUrl);
        }
      } else {
        // For web environment
        audio = new Audio(audioPath);
      }

      // Wait for audio to load
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error("Audio loading timeout"));
        }, 10000); // 10 second timeout

        audio.addEventListener("loadeddata", () => {
          clearTimeout(timeout);
          resolve();
        });
        audio.addEventListener("error", (e) => {
          clearTimeout(timeout);
          reject(e);
        });
        audio.load();
      });

      console.log("Audio loaded successfully:", {
        duration: audio.duration,
        src: audio.src,
      });

      // Generate waveform data for visualization
      const segmentDuration = selectedSegment.end - selectedSegment.start;
      const sampleCount = Math.ceil(segmentDuration * 20);
      const waveformData = [];

      // Generate realistic guitar waveform pattern
      for (let i = 0; i < sampleCount; i++) {
        const time = (i / sampleCount) * segmentDuration;
        const fundamental = Math.sin(time * 2 * Math.PI * 2) * 0.6;
        const harmonic2 = Math.sin(time * 2 * Math.PI * 4) * 0.3;
        const harmonic3 = Math.sin(time * 2 * Math.PI * 6) * 0.2;
        const decay = Math.exp(-time * 0.5);
        const variation = (Math.random() - 0.5) * 0.1;
        const amplitude =
          (fundamental + harmonic2 + harmonic3) * decay + variation;
        waveformData.push(Math.max(-1, Math.min(1, amplitude)));
      }

      // Set up audio to sync with video playback - IMPROVED LOOPING
      audio.loop = true;
      audio.preload = "auto"; // Ensure audio is preloaded for smooth looping

      // Add event listener for seamless looping
      audio.addEventListener("ended", () => {
        audio.currentTime = 0;
        if (!audio.paused) {
          audio.play();
        }
      });

      // Update segment with replaced audio data - ADD THE MISSING PROPERTIES HERE
      const updatedSegment = {
        ...selectedSegment,
        hasReplacedAudio: true,
        audioFile: audioFileName,
        audioPath: audioPath,
        audioElement: audio,
        waveformData: waveformData,
        audioDuration: audio.duration,
        shouldMuteOriginal: true,
        audioOverride: true,
        originalAudioMuted: true,
      };

      // Update segments using onSegmentsChange if available, otherwise use onSegmentUpdate
      if (onSegmentsChange && segments) {
        // Use onSegmentsChange to update the entire segments array (like VideoTimeline does)
        const updatedSegments = segments.map((seg) =>
          seg.id === selectedSegment.id ? updatedSegment : seg
        );
        onSegmentsChange(updatedSegments);
        console.log("✅ Audio replacement completed successfully");
      } else if (onSegmentUpdate) {
        // Fallback to individual segment update
        onSegmentUpdate(updatedSegment);
        console.log("✅ Audio replacement completed successfully");
      }

      console.log(
        `Audio successfully replaced for "${selectedSegment.name}". Audio will play automatically when video playhead enters this segment.`
      );

      return { success: true, updatedSegment };
    } catch (error) {
      console.error("Error replacing audio:", error);
      console.error(
        `Failed to replace audio: ${error.message}. Make sure the file exists at: ${audioPath}`
      );

      // Additional debugging information
      if (
        window.electronAPI &&
        typeof window.electronAPI.fileExists === "function"
      ) {
        const exists = window.electronAPI.fileExists(audioPath);
        console.log(`File exists check for ${audioPath}: ${exists}`);

        // Try to list files in the ai directory
        try {
          const separator = projectPath.includes("\\") ? "\\" : "/";
          const projectDir = projectPath.substring(
            0,
            projectPath.lastIndexOf(separator)
          );
          const aiDir = `${projectDir}${separator}ai`;
          console.log(`Checking ai directory: ${aiDir}`);

          if (
            window.electronAPI.directoryExists &&
            window.electronAPI.directoryExists(aiDir)
          ) {
            console.log("AI directory exists");
            if (window.electronAPI.listFiles) {
              const files = window.electronAPI.listFiles(aiDir);
              console.log("Files in ai directory:", files);
            }
          } else {
            console.log("AI directory does not exist");
          }
        } catch (dirError) {
          console.log("Could not check ai directory:", dirError);
        }
      }

      return { success: false, error: error.message };
    }
  };
  const createAiVideoAnalytics = (segments, audioFilenames = {}) => {
    const analytics = {
      destinationPath: projectUrl,
    };

    segments.forEach((segment, index) => {
      const segmentKey = `segment${index + 1}`;

      // Get saved settings for this segment, or use current settings as fallback
      const settings = segmentSettings[segment.id] || {
        characterSize: characterSize,
        weather: weather,
        environment: environtment,
        groundTexture: groundTexture,
        groundMaterial: groundMaterial,
        footwear: footwear,
        fullPrompt: fullPrompt,
        negativePrompt: negativePrompt,
        seed: seed,
        qualitySounds: qualitySounds,
        guidenceStrength: guidenceStrength,
      };

      analytics[segmentKey] = {
        segmentId: segment.id,
        segmentName: segment.name,
        startTime: segment.start,
        endTime: segment.end,
        audioFile: audioFilenames[segment.id] || null,
        characterSize: settings.characterSize,
        weather: settings.weather,
        environment: settings.environment,
        groundTexture: settings.groundTexture,
        groundMaterial: settings.groundMaterial,
        footwear: settings.footwear,
        fullPrompt: settings.fullPrompt,
        settings: {
          negativePrompt: settings.negativePrompt,
          seed: settings.seed,
          qualitySounds: settings.qualitySounds,
          guidenceStrength: settings.guidenceStrength,
        },
      };
    });

    return analytics;
  };
  // Updated handleGenerate function with better error handling
  const handleGenerate = async () => {
    if (!selectedSegment) {
      alert("Please select a segment to generate audio for");
      return;
    }

    try {
      setIsLoding(true);

      // Extract video filename using the same logic as in loadSegmentSettings
      let videoFileName = null;

      // Try to get filename from videoSrc first
      if (videoSrc && typeof videoSrc === "string") {
        const srcParts = videoSrc.split(/[/\\]/);
        const fullName = srcParts[srcParts.length - 1];
        // Remove file extension to get base name
        videoFileName = fullName.replace(/\.[^/.]+$/, "");
      }

      // If no videoSrc, try videoPathProps
      if (
        !videoFileName &&
        videoPathProps &&
        typeof videoPathProps === "string"
      ) {
        const pathParts = videoPathProps.split(/[/\\]/);
        const fullName = pathParts[pathParts.length - 1];
        // Remove file extension to get base name
        videoFileName = fullName.replace(/\.[^/.]+$/, "");
      }

      // If still no filename, try savedPath
      if (!videoFileName && savedPath) {
        const pathParts = savedPath.split(/[/\\]/);
        const fullName = pathParts[pathParts.length - 1];
        // Remove file extension to get base name
        videoFileName = fullName.replace(/\.[^/.]+$/, "");
      }

      console.log(
        `Using video filename for .plank save: ${
          videoFileName || "segment name fallback"
        }`
      );

      // IMPORTANT: Save current settings for this segment BEFORE generating
      // Pass the videoFileName so the .plank file uses the correct name
      const currentSegmentSettings = saveCurrentSettingsForSegment(
        selectedSegment.id,
        videoFileName
      );
      console.log(
        `Saved settings for segment ${selectedSegment.name}:`,
        currentSegmentSettings
      );

      // Create analytics with individual segment settings
      const currentAnalytics = createAiVideoAnalytics(
        segments,
        segmentAudioFiles
      );

      // Save project first to get the path
      const saveResult = await saveProject((prev) => ({
        ...prev,
        aiVideoAnalytics: currentAnalytics,
      }));

      if (!saveResult.success) {
        throw new Error(
          `Failed to save project: ${saveResult.error || "Unknown error"}`
        );
      }

      const projectPath = saveResult.path || saveResult.savedPath || projectUrl;
      const savedVideoFileName = saveResult.videoFileName || videoFileName; // Use extracted filename as fallback
      if (!projectPath) {
        throw new Error("No project path available after saving");
      }

      // Use the saved settings for this specific segment in the request
      const requestData = {
        destinationPath: projectPath,
        mock_mode: true,
        analysis_type: "audio_generation",
        videoFileName: savedVideoFileName,
        request_id: `req_${Date.now()}_${selectedSegment.id}`,
        selectedSegment: {
          id: selectedSegment.id,
          name: selectedSegment.name,
          start: selectedSegment.start,
          end: selectedSegment.end,
        },
        parameters: {
          weather: currentSegmentSettings.weather,
          environment: currentSegmentSettings.environment,
          ground_texture: currentSegmentSettings.groundTexture,
          ground_material: currentSegmentSettings.groundMaterial,
          footwear: currentSegmentSettings.footwear,
          seed: currentSegmentSettings.seed,
          quality_sounds: currentSegmentSettings.qualitySounds,
          guidance_strength: currentSegmentSettings.guidenceStrength,
          full_prompt: currentSegmentSettings.fullPrompt,
          negative_prompt: currentSegmentSettings.negativePrompt,
        },
      };

      console.log(
        `🚀 Starting Python analysis for segment: ${selectedSegment.name}`
      );

      // Call Python server
      const result = await window.electronAPI.runPythonAnalysis(requestData);
      if (result.success) {
        console.log("✅ Python analysis successful:", result.data);

        const generatedAudioFile = result.data.generated_file;
        // Wait for file system to sync
        console.log("⏳ Waiting for file system to sync...");
        await new Promise((resolve) => setTimeout(resolve, 3000));

        // Replace segment audio
        console.log("🔄 Replacing segment audio...");
        // const audioResult = await handleReplaceSegmentAudio(generatedAudioFile);

        const audioResult = await handleReplaceSegmentAudio(
          result.data.generated_file
        );

        if (audioResult.success) {
          // Update state with new audio filename
          setSegmentAudioFiles((prev) => ({
            ...prev,
            [selectedSegment.id]: generatedAudioFile,
          }));

          // Create updated analytics with new audio filename AND preserved settings
          const updatedAudioFiles = {
            ...segmentAudioFiles,
            [selectedSegment.id]: generatedAudioFile,
          };
          const updatedAnalytics = createAiVideoAnalytics(
            segments,
            updatedAudioFiles
          );

          // Save project with updated audio filename and individual settings
          await saveProject((prev) => ({
            ...prev,
            aiVideoAnalytics: updatedAnalytics,
          }));

          console.log(
            `✅ Audio generated for segment: ${selectedSegment.name}`
          );
          console.log(`📊 Segment settings preserved:`, currentSegmentSettings);
        }

        setIsLoding(false);
        console.log(
          `🎉 Process completed for segment: ${selectedSegment.name}!`
        );
      } else {
        setIsLoding(false);
        console.error("❌ Python analysis failed:", result.error);
        throw new Error(`Analysis failed: ${result.error}`);
      }
    } catch (error) {
      setIsLoding(false);
      console.error("❌ Error in generate process:", error);
      alert(`Error: ${error.message}`);
    }
  };

  return (
    <div
      className="flex lg:gap-3 xl:gap-6 justify-start items-start"
      key={`video-setting-${selectedSegment?.id || "no-segment"}`} // Force re-render on segment change
    >
      {/* Your existing JSX code remains unchanged */}
      <div className="relative">
        <div className="lg:w-9 xl:w-16 px-2 py-4 bg-gradient-to-bl from-stone-300/25 via-white/25 to-neutral-400/25 rounded-[40px] shadow-[inset_1px_1px_3px_0px_rgba(255,255,255,0.29)] outline-[0.50px] outline-offset-[-0.50px] backdrop-blur-[10px] flex flex-col justify-start items-center gap-3">
          <ItemSetting isActive={showPopup == "ai"}>
            <img
              onClick={() => {
                setShowPopup((prev) =>
                  prev == "" || prev !== "ai" ? "ai" : ""
                );
                setPage("home");
              }}
              src="./icon/sparkles.svg"
              alt="sparkles"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup == "setting"}>
            <img
              onClick={() => {
                setShowPopup((prev) =>
                  prev == "" || prev !== "setting" ? "setting" : ""
                );
                setPage("home");
              }}
              src="./icon/setting.svg"
              alt="setting"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup == "download"}>
            <img
              onClick={() => {
                setPage("home");
                setShowPopup((prev) =>
                  prev === "" || prev !== "download" ? "download" : ""
                );
              }}
              src="./icon/download.svg"
              alt="download"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting>
            <img
              src="./icon/user.svg"
              alt="user"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
          <ItemSetting isActive={showPopup == "help"}>
            <img
              onClick={() => {
                setPage("help");
                setShowPopup((prev) =>
                  prev === "" || prev !== "help" ? "help" : ""
                );
              }}
              src="./icon/help.svg"
              alt="help"
              className="lg:w-3 lg:h-3 xl:w-5 xl:h-5"
            />
          </ItemSetting>
        </div>
      </div>
      {showPopup === "ai" && (
        <PopupSetting key={`popup-${selectedSegment?.id || "no-segment"}`}>
          {" "}
          {/* Also force popup re-render */}
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              AI Video Analytics
            </h2>
            {selectedSegment && (
              <div className="text-sm font-medium text-blue-300 mb-2">
                📍 Selected: {selectedSegment.name} (ID: {selectedSegment.id})
              </div>
            )}
            <p className="text-sm font-normal font-inter leading-tight tracking-tight">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              enim ad minim veniam, quis nostrud exercitation ullamco laboris
              nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in
              reprehenderit in voluptate velit esse cillum dolore eu fugiat
              nulla pariatur.
            </p>
            <div className="character-size">
              <h3 className="text-sm font-semibold font-inter leading-tight tracking-tight">
                Character size
              </h3>
              <FormControl>
                <RadioGroup
                  row
                  aria-labelledby="demo-row-radio-buttons-group-label"
                  name="row-radio-buttons-group"
                  defaultValue={"big"}
                  value={characterSize}
                  onChange={(e) => setCharacterSize(e.target.value)}
                >
                  <FormControlLabel
                    value="big"
                    control={
                      <Radio
                        icon={<UncheckedIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label="Big"
                    sx={{
                      "& .MuiFormControlLabel-label": {
                        fontSize: "14px",
                        fontFamily: "Inter, sans-serif",
                        color: "white",
                      },
                    }}
                  />
                  <FormControlLabel
                    value="medium"
                    control={
                      <Radio
                        icon={<UncheckedIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label="Medium"
                    sx={{
                      "& .MuiFormControlLabel-label": {
                        fontSize: "14px",
                        fontFamily: "Inter, sans-serif",
                        color: "white",
                      },
                    }}
                  />
                  <FormControlLabel
                    value="small"
                    control={
                      <Radio
                        icon={<UncheckedIcon />}
                        checkedIcon={<CheckedIcon />}
                      />
                    }
                    label="Small"
                    sx={{
                      "& .MuiFormControlLabel-label": {
                        fontSize: "14px",
                        fontFamily: "Inter, sans-serif",
                        color: "white",
                      },
                    }}
                  />
                </RadioGroup>
              </FormControl>
            </div>
            <div className="environtment-criteria flex flex-col gap-2">
              <div className="text-sm font-semibold font-inter leading-tight tracking-tight">
                Environment criteria
              </div>
              <div className="pl-2 flex flex-col gap-2">
                <SelectEnv
                  heading="Weather"
                  defaultValue={weather}
                  data={weatherItems}
                  onchange={(e) => setWeather(e.target.value)}
                  key={`weather-${selectedSegment?.id || "default"}`} // Force re-render
                />
                <SelectEnv
                  heading="Environment"
                  defaultValue={environtment}
                  data={envItem}
                  onchange={(e) => setEnvirontment(e.target.value)}
                  key={`env-${selectedSegment?.id || "default"}`} // Force re-render
                />
                <SelectEnv
                  heading="Ground Texture"
                  defaultValue={groundTexture}
                  data={GroundTextureItems}
                  onchange={(e) => setGroundTexture(e.target.value)}
                  key={`texture-${selectedSegment?.id || "default"}`} // Force re-render
                />
                <SelectEnv
                  heading="Ground Material"
                  defaultValue={groundMaterial}
                  data={GroundMaterialItems}
                  onchange={(e) => setGroundMaterial(e.target.value)}
                  key={`material-${selectedSegment?.id || "default"}`} // Force re-render
                />
                <SelectEnv
                  heading="Footwear"
                  defaultValue={footwear}
                  data={FootwearItems}
                  onchange={(e) => setFootwear(e.target.value)}
                  key={`footwear-${
                    selectedSegment?.id || "default"
                  }-${footwear}`} // Force re-render with current value
                />
                {/* Full prompt */}
                <div className="flex flex-col gap-2">
                  <h4 className=" text-gray-200 text-sm font-normal font-inter leading-tight tracking-tight mb-0">
                    Full Prompt
                  </h4>
                  <Box
                    component="form"
                    sx={{
                      "& .MuiTextField-root": {
                        m: 0,
                        width: "100%",
                        border: "none",
                        backgroundColor: "#1818184D",
                        "& .MuiInputBase-input": {
                          color: "white", // This targets the actual input text
                          fontSize: "14px", // Font size for the input text
                          fontFamily: "Inter, sans-serif",
                        },
                        "& .MuiOutlinedInput-root": {
                          "& fieldset": {
                            border: "none",
                          },
                          "&:hover fieldset": {
                            border: "none",
                          },
                          "&.Mui-focused fieldset": {
                            border: "none",
                          },
                        },
                      },
                    }}
                    noValidate
                    autoComplete="off"
                  >
                    <TextField
                      id="outlined-multiline-static"
                      multiline
                      rows={4}
                      value={fullPrompt}
                      onChange={(e) => setFullPrompt(e.target.value)}
                      sx={{ color: "white" }}
                      key={`prompt-${selectedSegment?.id || "default"}`} // Force re-render
                    />
                  </Box>
                </div>
                {/* Video Information */}
                <div className="flex flex-col gap-2">
                  <h4 className=" text-gray-200 text-sm font-normal font-inter leading-tight tracking-tight mb-0">
                    Video Information
                  </h4>
                  <div className="flex flex-col gap-3 justify-between mt-3 px-5">
                    <p className="text-header-2">Processing Time: 00:00:44</p>
                    <p className="text-header-2">Length of Video: 00:00:06</p>
                    <p className="text-header-2">Size of Video: 1.01 MB</p>
                  </div>
                </div>
                <div className="flex flex-col gap-4 mt-8">
                  <Button
                    onClick={() => {
                      handleGenerate();
                    }}
                    disabled={!selectedSegment || isLoading}
                  >
                    {isLoading ? (
                      <img alt="loading" src="loading.gif" width={25} />
                    ) : !selectedSegment ? (
                      "Select a Segment to Generate"
                    ) : (
                      "Generate"
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </PopupSetting>
      )}

      {/* Your existing download and setting popups remain unchanged */}
      {showPopup === "download" && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              Download
            </h2>

            <div className="flex flex-col gap-6">
              <div className="flex flex-col">
                <SelectEnv
                  heading="Download (Audio + Video)"
                  defaultValue={videoType}
                  data={VideoTypeItems}
                  onchange={(e) => setVideoType(e.target.value)}
                />
                <Button customClass={"mt-4"} onClick={() => {}}>
                  Download
                </Button>
              </div>
              <div className="flex flex-col">
                <SelectEnv
                  heading="Download (Audio only)"
                  defaultValue={audioType}
                  data={AudioTypeItems}
                  onchange={(e) => setAudioType(e.target.value)}
                />
                <Button customClass={"mt-4"} onClick={() => {}}>
                  Download
                </Button>
              </div>
            </div>
          </div>
        </PopupSetting>
      )}

      {showPopup === "setting" && (
        <PopupSetting>
          <div className="flex flex-col gap-4 text-white p-4">
            <h2 className="text-sm font-bold font-inter leading-tight tracking-tight">
              Negative prompt
            </h2>
            <Box
              component="form"
              sx={{
                "& .MuiTextField-root": {
                  m: 0,
                  width: "100%",
                  border: "none",
                  backgroundColor: "#1818184D",
                  "& .MuiInputBase-input": {
                    color: "white", // This targets the actual input text
                    fontSize: "14px", // Font size for the input text
                    fontFamily: "Inter, sans-serif",
                  },
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      border: "none",
                    },
                    "&:hover fieldset": {
                      border: "none",
                    },
                    "&.Mui-focused fieldset": {
                      border: "none",
                    },
                  },
                },
              }}
              noValidate
              autoComplete="off"
            >
              <TextField
                id="outlined-multiline-static"
                multiline
                rows={6}
                value={negativePrompt}
                onChange={(e) => setNegativePrompt(e.target.value)}
                sx={{ color: "white" }}
              />
            </Box>

            <div className="flex flex-col gap-2">
              {/* Item slider */}
              <div className="flex flex-col">
                <h3>Seed: {seed}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(e) => setSeed(e.target.value)}
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={seed}
                    max={100}
                  />
                </Box>
              </div>
              {/* Item slider */}
              <div className="flex flex-col">
                <h3>Quality of Sounds: {qualitySounds}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(e) => setQualitySounds(e.target.value)}
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={qualitySounds}
                    max={100}
                  />
                </Box>
              </div>
              <div className="flex flex-col">
                <h3>Guidence Strength: {guidenceStrength}</h3>
                <Box sx={{ width: "80%" }}>
                  <Slider
                    onChange={(e) => setGuidenceStrength(e.target.value)}
                    aria-label="Default"
                    valueLabelDisplay="off"
                    value={guidenceStrength}
                    max={50}
                  />
                </Box>
              </div>
            </div>
          </div>
        </PopupSetting>
      )}
    </div>
  );
};

export default VideoSetting;
