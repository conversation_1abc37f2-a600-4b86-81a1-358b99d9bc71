import React, { useEffect, useState, useCallback } from "react";
import { Player, PlayerRef } from "@remotion/player";
import { Main } from "../../remotion/main";
import { useEditorContext } from "../../contexts/editor-context"; // Your existing context
import { FPS } from "../../constants";
import { Upload, Video, Plus } from "lucide-react";
import VideoSetting from "@/components/editor/planck-version/components/VideoSetting";

/**
 * Props for the VideoPlayer component
 * @interface VideoPlayerProps
 * @property {React.RefObject<PlayerRef>} playerRef - Reference to the Remotion player instance
 * @property {(files: File[]) => void} onFilesAdded - Callback for when files are dropped
 */
interface VideoPlayerProps {
  playerRef: React.RefObject<PlayerRef>;
  onFilesAdded: (files: File[]) => void; // Make this required
}

// Supported file types for drag and drop
const ACCEPTED_FILE_TYPES = {
  video: [".mp4", ".mov", ".avi", ".mkv", ".webm", ".m4v"],
  audio: [".mp3", ".wav", ".aac", ".m4a", ".ogg", ".flac"],
  image: [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"],
};

const ALL_ACCEPTED_TYPES = [
  ...ACCEPTED_FILE_TYPES.video,
  ...ACCEPTED_FILE_TYPES.audio,
  ...ACCEPTED_FILE_TYPES.image,
];

/**
 * Empty State Component for when no overlays are present
 */
const EmptyPlayerState: React.FC<{ onFilesAdded: (files: File[]) => void }> = ({
  onFilesAdded,
}) => {
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      onFilesAdded(files);
    }
    // Reset input
    e.target.value = "";
  };

  return (
    <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-[16px] overflow-hidden">
      <div className="text-center p-8 max-w-md mx-auto">
        <div className="flex justify-center mb-6">
          <div className="relative">
            <div className="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <Video className="w-10 h-10 text-gray-400 dark:text-gray-500" />
            </div>
            <div className="absolute -top-1 -right-1 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
              <Plus className="w-4 h-4 text-white" />
            </div>
          </div>
        </div>

        <div className="w-64 text-center justify-start text-white text-base font-medium leading-snug tracking-tight">
          Drop a video here to start working
        </div>

        <div className="mt-4">
          <div>
            <label className="inline-flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg cursor-pointer transition-colors shadow-lg hover:shadow-xl">
              <Upload className="w-4 h-4 mr-2" />
              Choose Files
              <input
                type="file"
                multiple
                accept={ALL_ACCEPTED_TYPES.join(",")}
                onChange={handleFileUpload}
                className="hidden"
              />
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * VideoPlayer component that renders a responsive video editor with overlay support and drag & drop
 * The player automatically resizes based on its container and maintains the specified aspect ratio
 */
export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  playerRef,
  onFilesAdded,
}) => {
  const {
    overlays,
    setSelectedOverlayId,
    changeOverlay,
    selectedOverlayId,
    aspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    durationInFrames,
    isProcessing,
    segments,
    selectedSegment,
    setSelectedSegment,
    updateSegment,
    onSegmentsChange,
  } = useEditorContext();

  // Drag and drop state
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);

  // Check if player is empty (no overlays)
  const isEmpty = overlays.length === 0;

  // Wrapper function to handle segment updates
  const handleSegmentUpdate = useCallback(
    (updatedSegment: any) => {
      if (updatedSegment && updatedSegment.id) {
        updateSegment(updatedSegment.id, updatedSegment);
      }
    },
    [updateSegment]
  );

  /**
   * Validates if a file is supported
   */
  const isValidFile = useCallback((file: File): boolean => {
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    return ALL_ACCEPTED_TYPES.includes(extension);
  }, []);

  /**
   * Drag and drop event handlers
   */
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter((prev) => prev + 1);

    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  }, []);

  const handleDragLeave = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragCounter((prev) => prev - 1);

      if (dragCounter <= 1) {
        setIsDragOver(false);
      }
    },
    [dragCounter]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();

      setIsDragOver(false);
      setDragCounter(0);

      const files = Array.from(e.dataTransfer.files);
      const validFiles = files.filter(isValidFile);

      if (validFiles.length > 0) {
        onFilesAdded(validFiles);
      }
    },
    [onFilesAdded, isValidFile]
  );

  /**
   * Updates the player dimensions when the container size or aspect ratio changes
   */
  useEffect(() => {
    const handleDimensionUpdate = () => {
      const videoContainer = document.querySelector(".video-container");
      if (!videoContainer) return;

      const { width, height } = videoContainer.getBoundingClientRect();
      updatePlayerDimensions(width, height);
    };

    handleDimensionUpdate(); // Initial update
    window.addEventListener("resize", handleDimensionUpdate);

    return () => {
      window.removeEventListener("resize", handleDimensionUpdate);
    };
  }, [aspectRatio, updatePlayerDimensions]);

  const { width: compositionWidth, height: compositionHeight } =
    getAspectRatioDimensions();

  // Constants for player configuration
  const PLAYER_CONFIG = {
    durationInFrames: Math.max(Math.round(durationInFrames || 900), 1), // Ensure minimum duration
    fps: FPS,
  };

  return (
    <div className="w-full h-full overflow-hidden">
      {/* Grid background container with drag and drop support */}
      <div
        className={`z-0 video-container relative w-full h-full main-layout
        ${
          isDragOver
            ? "ring-2 ring-blue-500 ring-inset bg-blue-50/50 dark:bg-blue-900/20"
            : ""
        }
        ${isProcessing ? "opacity-75" : ""}`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {/* Player wrapper with centering */}
        <div className="z-10 absolute inset-2 sm:inset-4 flex items-center justify-center rounded-[16px] ">
          <div className="flex gap-10">
            <div
              className="relative mx-2 sm:mx-0"
              style={{
                width: Math.min(playerDimensions.width, compositionWidth),
                height: Math.min(playerDimensions.height, compositionHeight),
                maxWidth: "100%",
                maxHeight: "100%",
              }}
            >
              {/* Show empty state if no overlays, otherwise show player */}
              {isEmpty ? (
                <div className="w-full h-full min-h-[300px] relative player-wrapper">
                  <EmptyPlayerState onFilesAdded={onFilesAdded} />
                </div>
              ) : (
                <Player
                  ref={playerRef}
                  className="w-full h-full player-wrapper rounded-[16px] !overflow-hidden"
                  component={Main}
                  compositionWidth={compositionWidth}
                  compositionHeight={compositionHeight}
                  style={{
                    width: "100%",
                    height: "100%",
                  }}
                  durationInFrames={PLAYER_CONFIG.durationInFrames}
                  fps={PLAYER_CONFIG.fps}
                  inputProps={{
                    overlays,
                    setSelectedOverlayId,
                    changeOverlay,
                    selectedOverlayId,
                    durationInFrames: PLAYER_CONFIG.durationInFrames,
                    fps: FPS,
                    width: compositionWidth,
                    height: compositionHeight,
                  }}
                  errorFallback={() => <></>}
                  overflowVisible
                />
              )}
            </div>

            {/* Segment Selection Bar */}
            {!isEmpty && segments.length > 0 && (
              <div className="mt-4 p-4 bg-gray-800 rounded-lg">
                <h3 className="text-white text-sm font-medium mb-3">
                  Select a Segment to Generate
                </h3>
                <div className="flex gap-2 flex-wrap">
                  {segments.map((segment) => (
                    <button
                      key={segment.id}
                      onClick={() => setSelectedSegment(segment)}
                      className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        selectedSegment?.id === segment.id
                          ? "bg-blue-500 text-white"
                          : "bg-gray-600 text-gray-200 hover:bg-gray-500"
                      }`}
                    >
                      {segment.name}
                      {segment.hasReplacedAudio && (
                        <span className="ml-1 text-green-400">🎵</span>
                      )}
                    </button>
                  ))}
                </div>
                {selectedSegment && (
                  <div className="mt-2 text-xs text-gray-400">
                    Selected: {selectedSegment.name} (
                    {selectedSegment.start.toFixed(1)}s -{" "}
                    {selectedSegment.end.toFixed(1)}s)
                  </div>
                )}
              </div>
            )}

            {/* Video Setting */}
            {!isEmpty && (
              <VideoSetting
                videoSrc={null}
                videoPathProps={null}
                selectedSegment={selectedSegment}
                onSegmentUpdate={handleSegmentUpdate}
                segments={segments}
                onSegmentsChange={onSegmentsChange}
                segmentSettingsCache={{}}
              />
            )}
          </div>
        </div>

        {/* Drag overlay indicator */}
        {isDragOver && (
          <div className="absolute inset-0 z-50 flex items-center justify-center bg-blue-500/10 backdrop-blur-sm border-2 border-dashed border-blue-500 rounded-lg">
            <div className="text-center p-8">
              <Upload className="mx-auto h-16 w-16 text-blue-500 mb-4 animate-bounce" />
              <h3 className="text-xl font-semibold text-blue-600 dark:text-blue-400 mb-2">
                Drop files here
              </h3>
              <p className="text-sm text-blue-600/80 dark:text-blue-400/80">
                Release to add media to your timeline
              </p>
            </div>
          </div>
        )}

        {/* Processing indicator overlay */}
        {isProcessing && (
          <div className="absolute inset-0 z-40 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent mx-auto mb-3" />
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                Processing files...
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Please wait while we add your media
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
