/* eslint-disable no-unused-vars */
import { useEffect, useRef, useCallback, useState } from "react";
import { Buff<PERSON> } from "buffer";
import path from "path";

// Type declarations for Electron API
declare global {
  interface Window {
    electronAPI: {
      saveVideo: (
        videoPath: string,
        buffer: Buffer,
        hash: string,
        analytics: Record<string, any>
      ) => Promise<{
        success: boolean;
        path?: string;
        data?: { savedPath: string };
        error?: string;
      }>;
      onMenuSave: (callback: () => void) => void;
      onClearProject: (callback: () => void) => void;
      removeAllListeners: (event: string) => void;
    };
  }
}

export const useSaveProject = (
  videoSrc: string | null,
  videoFile: File | null,
  aiVideoAnalytics: Record<string, any>
) => {
  const videoSrcRef = useRef(videoSrc);
  const videoFileRef = useRef(videoFile);
  const lastSavedHashRef = useRef<string | null>(null);
  const lastSavedPathRef = useRef<string | null>(null);
  const [savedPath, setSavedPath] = useState<string | null>(null);
  const [videoFileName, setVideoFileName] = useState<string>("");

  useEffect(() => {
    videoSrcRef.current = videoSrc;
  }, [videoSrc]);

  useEffect(() => {
    videoFileRef.current = videoFile;
  }, [videoFile]);

  const getFileHash = async (blob: Blob) => {
    const arrayBuffer = await blob.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest("SHA-256", arrayBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  };

  const saveProject = useCallback(async (updateFunc: ((prev: { aiVideoAnalytics: Record<string, any> }) => { aiVideoAnalytics: Record<string, any> }) | null = null) => {
    const currentSrc = videoSrcRef.current;
    const currentFile = videoFileRef.current;

    if (!currentSrc || !currentFile) {
      alert("🚫 Tidak ada video untuk disimpan.");
      return { success: false, error: "No video to save" };
    }

    try {
      let result;
      let finalAiVideoAnalytics = aiVideoAnalytics;

      // If updateFunc is provided, use it to update aiVideoAnalytics
      if (updateFunc && typeof updateFunc === "function") {
        const updated = updateFunc({ aiVideoAnalytics });
        finalAiVideoAnalytics = updated.aiVideoAnalytics || updated;
        console.log("📊 Updated aiVideoAnalytics:", finalAiVideoAnalytics);
      }

      const response = await fetch(currentSrc);
      const blob = await response.blob();
      const buffer = Buffer.from(await blob.arrayBuffer());
      const currentHash = await getFileHash(blob);

      const isSameFile = currentHash === lastSavedHashRef.current;

      const videoPath =
        isSameFile && lastSavedPathRef.current
          ? lastSavedPathRef.current
          : currentFile.name;

      // Save the video/project
      result = await window.electronAPI.saveVideo(
        videoPath,
        buffer,
        currentHash,
        finalAiVideoAnalytics
      );

      console.log("💾 Save result:", result);

      if (result.success) {
        lastSavedHashRef.current = currentHash;
        lastSavedPathRef.current = result.path || null;
        setSavedPath(result.path || null);
        setVideoFileName(result.data?.savedPath || "");

        // Add this to store the last saved path
        const savedPath = result.path || "";

        // Return the saved path along with other info
        return {
          success: true,
          path: savedPath,
          videoFileName: result.data?.savedPath || "",
          message: `Video berhasil disimpan di: ${savedPath}`,
          aiCopy: undefined, // Optional property for compatibility
        };
      }
    } catch (error) {
      console.error("saveProject error:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      alert(`❌ Terjadi kesalahan: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, [aiVideoAnalytics]);

  const clearProject = useCallback(() => {
    videoSrcRef.current = null;
    videoFileRef.current = null;
    // Jangan reset lastSavedHashRef/lastSavedPathRef
  }, []);

  useEffect(() => {
    if (!window.electronAPI?.onMenuSave) return;

    const handleSave = async () => {
      const result = await saveProject();
      if (result && result.success) {
        let message = result.message;
        if ((result as any).aiCopy && (result as any).aiCopy.success) {
          message += `\n🔧 AI folder copied for development testing.`;
        }
      }
    };

    window.electronAPI.onMenuSave(handleSave);
    window.electronAPI.onClearProject(clearProject);

    return () => {
      window.electronAPI.removeAllListeners("save-project");
    };
  }, [clearProject, saveProject]);

  return {
    clearProject,
    saveProject,
    path,
    savedPath,
    videoFileName,
  };
};
